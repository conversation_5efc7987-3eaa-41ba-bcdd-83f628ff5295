using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using View = Autodesk.Revit.DB.View;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of ITaggingService for creating and managing Revit tags
    /// </summary>
    public class TaggingService : ITaggingService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private readonly IRevitParameterService _parameterService;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the tagging service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        /// <param name="parameterService">Parameter service for reading cable data</param>
        public TaggingService(Document document, BecaActivityLoggerData logger, IRevitParameterService parameterService)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
            _parameterService = parameterService ?? throw new ArgumentNullException(nameof(parameterService));
        }

        #endregion

        #region Tag Creation

        /// <summary>
        /// Create a tag for a cable tray with cable information
        /// </summary>
        public IndependentTag CreateTrayTag(CableTray cableTray, CableTraySegmentModel traySegment)
        {
            try
            {
                if (cableTray == null || traySegment == null) return null;

                // Get the active view
                var activeView = _document.ActiveView;
                if (activeView == null)
                {
                    _logger?.Log("No active view for tag creation", LogType.Warning);
                    return null;
                }

                // Calculate tag position
                var tagPosition = CalculateOptimalTagPosition(cableTray, activeView);
                if (tagPosition == null) return null;

                // Get tag type
                var tagType = GetOrCreateCableTrayTagType();
                if (tagType == null)
                {
                    _logger?.Log("Could not find or create cable tray tag type", LogType.Error);
                    return null;
                }

                // Create the tag
                using (var transaction = new Transaction(_document, "Create Cable Tray Tag"))
                {
                    transaction.Start();

                    var tag = IndependentTag.Create(_document, tagType.Id, activeView.Id, 
                        new Reference(cableTray), false, TagOrientation.Horizontal, tagPosition);

                    if (tag != null)
                    {
                        // Configure tag properties
                        ConfigureTagProperties(tag, traySegment);
                        
                        transaction.Commit();
                        _logger?.Log($"Created tag for tray {traySegment.TrayRef}", LogType.Information);
                        return tag;
                    }
                    else
                    {
                        transaction.RollBack();
                        _logger?.Log($"Failed to create tag for tray {traySegment.TrayRef}", LogType.Error);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating tray tag: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Create tags for multiple cable trays
        /// </summary>
        public List<IndependentTag> CreateMultipleTrayTags(List<CableTraySegmentModel> traySegments, TaggingOptions options)
        {
            var createdTags = new List<IndependentTag>();

            try
            {
                if (traySegments == null || traySegments.Count == 0) return createdTags;

                using (var transaction = new Transaction(_document, "Create Multiple Cable Tray Tags"))
                {
                    transaction.Start();

                    foreach (var traySegment in traySegments)
                    {
                        try
                        {
                            if (options.SkipExistingTags && HasExistingTag(traySegment.CableTray))
                                continue;

                            var tag = CreateTrayTag(traySegment.CableTray, traySegment);
                            if (tag != null)
                            {
                                createdTags.Add(tag);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.Log($"Failed to create tag for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                        }
                    }

                    transaction.Commit();
                    _logger?.Log($"Created {createdTags.Count} tags out of {traySegments.Count} tray segments", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating multiple tray tags: {ex.Message}", LogType.Error);
            }

            return createdTags;
        }

        /// <summary>
        /// Create a tag at a specific position
        /// </summary>
        public IndependentTag CreateTagAtPosition(CableTray cableTray, XYZ position, TagOrientation orientation)
        {
            try
            {
                if (cableTray == null || position == null) return null;

                var activeView = _document.ActiveView;
                if (activeView == null) return null;

                var tagType = GetOrCreateCableTrayTagType();
                if (tagType == null) return null;

                using (var transaction = new Transaction(_document, "Create Tag at Position"))
                {
                    transaction.Start();

                    var tag = IndependentTag.Create(_document, tagType.Id, activeView.Id,
                        new Reference(cableTray), false, orientation, position);

                    if (tag != null)
                    {
                        transaction.Commit();
                        _logger?.Log($"Created tag at position for tray {cableTray.Id}", LogType.Information);
                        return tag;
                    }
                    else
                    {
                        transaction.RollBack();
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating tag at position: {ex.Message}", LogType.Error);
                return null;
            }
        }

        #endregion

        #region Tag Management

        /// <summary>
        /// Update tag content with latest cable information
        /// </summary>
        public bool UpdateTagContent(IndependentTag tag, CableTraySegmentModel traySegment)
        {
            try
            {
                if (tag == null || traySegment == null) return false;

                using (var transaction = new Transaction(_document, "Update Tag Content"))
                {
                    transaction.Start();

                    // Update tag properties based on current tray segment data
                    ConfigureTagProperties(tag, traySegment);

                    transaction.Commit();
                    _logger?.Log($"Updated tag content for tray {traySegment.TrayRef}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error updating tag content: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Remove a tag from the document
        /// </summary>
        public bool RemoveTag(IndependentTag tag)
        {
            try
            {
                if (tag == null || !tag.IsValidObject) return false;

                using (var transaction = new Transaction(_document, "Remove Tag"))
                {
                    transaction.Start();

                    _document.Delete(tag.Id);

                    transaction.Commit();
                    _logger?.Log($"Removed tag {tag.Id}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error removing tag: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find existing tags for a cable tray
        /// </summary>
        public List<IndependentTag> FindExistingTags(CableTray cableTray)
        {
            var existingTags = new List<IndependentTag>();

            try
            {
                if (cableTray == null) return existingTags;

                var activeView = _document.ActiveView;
                if (activeView == null) return existingTags;

                // Find all tags in the active view
                var collector = new FilteredElementCollector(_document, activeView.Id)
                    .OfClass(typeof(IndependentTag));

                foreach (IndependentTag tag in collector)
                {
                    try
                    {
                        // Get the tagged element using the tag's reference
                        var taggedElementIds = tag.GetTaggedLocalElementIds();
                        if (taggedElementIds != null && taggedElementIds.Contains(cableTray.Id))
                        {
                            existingTags.Add(tag);
                        }
                    }
                    catch
                    {
                        // Skip tags that can't be processed
                    }
                }

                _logger?.Log($"Found {existingTags.Count} existing tags for tray {cableTray.Id}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding existing tags: {ex.Message}", LogType.Error);
            }

            return existingTags;
        }

        /// <summary>
        /// Check if a cable tray already has a tag
        /// </summary>
        public bool HasExistingTag(CableTray cableTray)
        {
            try
            {
                var existingTags = FindExistingTags(cableTray);
                return existingTags.Count > 0;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error checking for existing tag: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Tag Positioning

        /// <summary>
        /// Calculate optimal position for a tag
        /// </summary>
        public XYZ CalculateOptimalTagPosition(CableTray cableTray, View view)
        {
            try
            {
                if (cableTray == null || view == null) return null;

                // Get tray bounding box
                var boundingBox = cableTray.get_BoundingBox(view);
                if (boundingBox == null) return null;

                // Calculate position above the tray
                var center = (boundingBox.Min + boundingBox.Max) * 0.5;
                var offset = new XYZ(0, 0, 2.0); // 2 feet above

                return center + offset;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating tag position: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Adjust tag position to avoid overlaps
        /// </summary>
        public XYZ AdjustTagPositionForOverlaps(XYZ proposedPosition, View view, double avoidanceRadius)
        {
            try
            {
                if (proposedPosition == null || view == null) return proposedPosition;

                // Find existing tags in the area
                var nearbyTags = FindTagsInArea(proposedPosition, avoidanceRadius, view);
                
                if (nearbyTags.Count == 0) return proposedPosition;

                // Calculate adjusted position
                var adjustedPosition = proposedPosition;
                var attempts = 0;
                var maxAttempts = 8;

                while (attempts < maxAttempts)
                {
                    var hasOverlap = false;
                    foreach (var tag in nearbyTags)
                    {
                        var tagPosition = GetTagPosition(tag);
                        if (tagPosition != null && tagPosition.DistanceTo(adjustedPosition) < avoidanceRadius)
                        {
                            hasOverlap = true;
                            break;
                        }
                    }

                    if (!hasOverlap) break;

                    // Try a new position in a spiral pattern
                    var angle = attempts * Math.PI / 4; // 45-degree increments
                    var offset = new XYZ(
                        Math.Cos(angle) * avoidanceRadius,
                        Math.Sin(angle) * avoidanceRadius,
                        0);

                    adjustedPosition = proposedPosition + offset;
                    attempts++;
                }

                return adjustedPosition;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error adjusting tag position: {ex.Message}", LogType.Error);
                return proposedPosition;
            }
        }

        /// <summary>
        /// Get optimal tag positions for multiple trays
        /// </summary>
        public Dictionary<CableTray, XYZ> GetOptimalTagPositions(List<CableTray> cableTrays, View view, TagPositioningOptions options)
        {
            var positions = new Dictionary<CableTray, XYZ>();

            try
            {
                if (cableTrays == null || view == null) return positions;

                foreach (var cableTray in cableTrays)
                {
                    var position = CalculateOptimalTagPosition(cableTray, view);
                    if (position != null)
                    {
                        if (options.AvoidOverlaps)
                        {
                            position = AdjustTagPositionForOverlaps(position, view, options.AvoidanceRadius);
                        }
                        positions[cableTray] = position;
                    }
                }

                _logger?.Log($"Calculated positions for {positions.Count} tags", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating optimal tag positions: {ex.Message}", LogType.Error);
            }

            return positions;
        }

        #endregion

        #region Tag Types and Families

        /// <summary>
        /// Get or create a cable tray tag type
        /// </summary>
        public FamilySymbol GetOrCreateCableTrayTagType()
        {
            try
            {
                // First try to find existing cable tray tag type
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(FamilySymbol))
                    .OfCategory(BuiltInCategory.OST_CableTrayTags);

                var existingTagType = collector.FirstOrDefault() as FamilySymbol;
                if (existingTagType != null)
                {
                    if (!existingTagType.IsActive)
                    {
                        using (var transaction = new Transaction(_document, "Activate Tag Type"))
                        {
                            transaction.Start();
                            existingTagType.Activate();
                            transaction.Commit();
                        }
                    }
                    return existingTagType;
                }

                // If no existing tag type, try to load a default one
                // This would typically involve loading a family file
                _logger?.Log("No cable tray tag type found - would need to load family", LogType.Warning);
                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting cable tray tag type: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Load a tag family from file
        /// </summary>
        public bool LoadTagFamily(string familyPath)
        {
            try
            {
                if (string.IsNullOrEmpty(familyPath)) return false;

                using (var transaction = new Transaction(_document, "Load Tag Family"))
                {
                    transaction.Start();

                    var success = _document.LoadFamily(familyPath);

                    if (success)
                    {
                        transaction.Commit();
                        _logger?.Log($"Loaded tag family from {familyPath}", LogType.Information);
                        return true;
                    }
                    else
                    {
                        transaction.RollBack();
                        _logger?.Log($"Failed to load tag family from {familyPath}", LogType.Error);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error loading tag family: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate tag creation requirements
        /// </summary>
        public List<string> ValidateTagCreationRequirements(CableTray cableTray, View view)
        {
            var issues = new List<string>();

            try
            {
                if (cableTray == null)
                {
                    issues.Add("Cable tray is null");
                    return issues;
                }

                if (view == null)
                {
                    issues.Add("View is null");
                    return issues;
                }

                if (!cableTray.IsValidObject)
                {
                    issues.Add("Cable tray is not a valid object");
                }

                if (view.ViewType == ViewType.ThreeD)
                {
                    issues.Add("Cannot create tags in 3D views");
                }

                var tagType = GetOrCreateCableTrayTagType();
                if (tagType == null)
                {
                    issues.Add("No cable tray tag type available");
                }

                var boundingBox = cableTray.get_BoundingBox(view);
                if (boundingBox == null)
                {
                    issues.Add("Cable tray is not visible in the current view");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Check if view supports tagging
        /// </summary>
        public bool IsViewSuitableForTagging(View view)
        {
            try
            {
                if (view == null) return false;

                // Tags can be created in plan, elevation, and section views
                return view.ViewType == ViewType.FloorPlan ||
                       view.ViewType == ViewType.CeilingPlan ||
                       view.ViewType == ViewType.Elevation ||
                       view.ViewType == ViewType.Section;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Configure tag properties based on tray segment data
        /// </summary>
        private void ConfigureTagProperties(IndependentTag tag, CableTraySegmentModel traySegment)
        {
            try
            {
                if (tag == null || traySegment == null) return;

                // Set tag text to show cable information
                var cableInfo = FormatCableInformation(traySegment);
                
                // Try to set tag text (this depends on the tag family parameters)
                var textParam = tag.get_Parameter(BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS);
                if (textParam != null && !textParam.IsReadOnly)
                {
                    textParam.Set(cableInfo);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring tag properties: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Format cable information for display in tag
        /// </summary>
        private string FormatCableInformation(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.Cables == null || traySegment.Cables.Count == 0)
                {
                    return $"{traySegment?.TrayRef ?? "Unknown"}\nNo cables";
                }

                var cableRefs = string.Join(", ", traySegment.Cables.Take(5).Select(c => c.CableRef));
                if (traySegment.Cables.Count > 5)
                {
                    cableRefs += $" +{traySegment.Cables.Count - 5} more";
                }

                return $"{traySegment.TrayRef}\n{cableRefs}";
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error formatting cable information: {ex.Message}", LogType.Warning);
                return traySegment?.TrayRef ?? "Unknown";
            }
        }

        /// <summary>
        /// Find tags in a specific area
        /// </summary>
        private List<IndependentTag> FindTagsInArea(XYZ center, double radius, View view)
        {
            var tagsInArea = new List<IndependentTag>();

            try
            {
                var collector = new FilteredElementCollector(_document, view.Id)
                    .OfClass(typeof(IndependentTag));

                foreach (IndependentTag tag in collector)
                {
                    var tagPosition = GetTagPosition(tag);
                    if (tagPosition != null && tagPosition.DistanceTo(center) <= radius)
                    {
                        tagsInArea.Add(tag);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding tags in area: {ex.Message}", LogType.Warning);
            }

            return tagsInArea;
        }

        /// <summary>
        /// Get the position of a tag
        /// </summary>
        private XYZ GetTagPosition(IndependentTag tag)
        {
            try
            {
                return tag?.TagHeadPosition;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}
