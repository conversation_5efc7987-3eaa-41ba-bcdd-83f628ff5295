# Traylor Swift - Installation and Setup Guide 🛠️

## Overview
This guide covers the complete installation and setup process for the Traylor Swift MEP plugin, including prerequisites, installation steps, project configuration, and initial testing.

---

## 📋 **Prerequisites**

### **Software Requirements**
- **Autodesk Revit** 2019 or later
- **Windows 10/11** (64-bit)
- **.NET Framework 4.8** or later
- **MEP Worksets** (for multi-user projects)

### **Project Requirements**
- **MEP Project Template** with electrical systems
- **Cable Tray Families** properly loaded
- **Electrical Circuit Definitions** in the project
- **BecaParametricCable Family** (for 3D visualization)

### **User Permissions**
- **Revit Add-in Installation Rights** (Administrator or local install)
- **Project Write Access** (for parameter updates)
- **Family Loading Rights** (for tag and parametric cable families)

---

## 💾 **Installation Process**

### **Step 1: Download Plugin Files**
1. Obtain the Traylor Swift plugin package
2. Extract to a temporary folder
3. Verify all files are present:
   ```
   MEP.TraylorSwift.dll
   MEP.TraylorSwift.addin
   BecaActivityLogger.dll
   CommunityToolkit.Mvvm.dll
   MaterialDesignThemes.Wpf.dll
   MaterialDesignColors.dll
   ```

### **Step 2: Install Plugin**
1. **Locate Revit Add-ins Folder:**
   - **All Users:** `C:\ProgramData\Autodesk\Revit\Addins\[Version]\`
   - **Current User:** `%APPDATA%\Autodesk\Revit\Addins\[Version]\`

2. **Copy Files:**
   - Copy `MEP.TraylorSwift.addin` to the Add-ins folder
   - Copy all `.dll` files to the Add-ins folder or subfolder

3. **Verify Installation:**
   - Files should be in: `C:\ProgramData\Autodesk\Revit\Addins\2024\`
   - Check file permissions (not blocked)

### **Step 3: Configure Revit**
1. **Launch Revit**
2. **Check Add-ins Tab** - Traylor Swift button should appear
3. **Test Launch** - Click button to verify plugin loads

---

## 🏗️ **Project Setup**

### **Step 1: Prepare MEP Project**
1. **Open/Create MEP Project** using appropriate template
2. **Load Cable Tray Families** if not already present
3. **Model Cable Trays** with proper routing and connections
4. **Define Electrical Circuits** with equipment connections

### **Step 2: Add Required Parameters**
Add these shared parameters to Cable Tray families:

| Parameter Name | Type | Group | Description |
|----------------|------|-------|-------------|
| **TS_Cables** | Text | Data | Detected cable references |
| **TS_SectionView** | Text | Data | Associated section view name |
| **TS_ParametricCable** | Text | Data | Parametric cable element ID |

**Parameter Creation Steps:**
1. **Manage Tab** → **Project Parameters**
2. **Add** → **Shared Parameter**
3. **Create/Select Parameter File**
4. **Add Parameters** with specifications above
5. **Apply to Cable Tray Category**

### **Step 3: Load Required Families**
1. **Cable Tray Tag Family**
   - Load appropriate tag family for your standards
   - Ensure it can display text parameters
   - Test tag creation manually

2. **BecaParametricCable Family**
   - Load the parametric cable family
   - Verify it has required parameters
   - Test manual placement

### **Step 4: Configure Views**
1. **Create Floor Plans** for each level with cable trays
2. **Set Appropriate Scales** (1:100 or 1:50 typical)
3. **Configure Visibility/Graphics** to show cable trays clearly
4. **Create 3D Views** for visualization (optional)

---

## ⚙️ **Initial Configuration**

### **Step 1: Test Basic Functionality**
1. **Launch Traylor Swift** from Add-ins tab
2. **Load Data** - Should find cable trays and circuits
3. **Check Grid Display** - Verify tray segments appear
4. **Test Level Filter** - Should filter by building levels

### **Step 2: Verify Cable Detection**
1. **Add Tray Segment** - Select a cable tray
2. **Detect Cables** - Should find associated circuits
3. **Review Results** - Check cable count and references
4. **Validate Accuracy** - Compare with manual inspection

### **Step 3: Test Tagging**
1. **Switch to Floor Plan View**
2. **Select Tray Segments**
3. **Create Tags** - Should place tags automatically
4. **Check Tag Content** - Verify information display

### **Step 4: Test Section Views**
1. **Configure Tray Segment**
2. **Create Section View** - Should generate cross-section
3. **Review Section** - Check scale and visibility
4. **Verify Parameter Update** - TS_SectionView should be populated

---

## 🔧 **Advanced Configuration**

### **Custom Tag Families**
1. **Create Custom Tag Family** matching your standards
2. **Add Required Parameters** for cable information
3. **Load in Project** and set as default
4. **Test with Traylor Swift** tagging functionality

### **Parametric Cable Customization**
1. **Modify BecaParametricCable Family** if needed
2. **Add Company-Specific Parameters**
3. **Configure Cable Arrangements** for your standards
4. **Test Different Spacing Options**

### **View Templates**
1. **Create Section View Template** for consistency
2. **Set Standard Scales** and visibility settings
3. **Configure for Cable Documentation**
4. **Apply to Generated Sections**

### **Project Standards**
1. **Establish Naming Conventions** for sections and views
2. **Define Standard Cable Arrangements** (trefoil vs flat)
3. **Set Default Spacing Requirements** (touching, D, 2D)
4. **Create Documentation Standards**

---

## 🧪 **Testing and Validation**

### **Functionality Checklist**
- [ ] Plugin loads without errors
- [ ] Data loading works with your project
- [ ] Cable detection finds appropriate circuits
- [ ] Tagging works in floor plan views
- [ ] Section view creation succeeds
- [ ] Parametric cable creation works
- [ ] Cable viewer displays all cables
- [ ] 3D view creation functions properly

### **Performance Testing**
1. **Test with Small Project** (< 50 cable trays)
2. **Test with Medium Project** (50-200 cable trays)
3. **Test with Large Project** (> 200 cable trays)
4. **Monitor Memory Usage** during operations
5. **Check Response Times** for each function

### **Data Integrity Testing**
1. **Verify Parameter Updates** are saved correctly
2. **Check Cross-References** between elements
3. **Test Undo/Redo** functionality
4. **Validate Multi-User** scenarios (if applicable)

---

## 🚨 **Troubleshooting Installation**

### **Plugin Doesn't Appear**
1. **Check File Locations** - Verify correct Add-ins folder
2. **Check File Permissions** - Unblock if necessary
3. **Verify .addin File** - Check XML syntax
4. **Restart Revit** - Required after installation
5. **Check Revit Version** - Ensure compatibility

### **Plugin Loads But Crashes**
1. **Check Dependencies** - Verify all DLL files present
2. **Check .NET Version** - Ensure 4.8 or later
3. **Review Error Messages** - Check Revit journal files
4. **Test with Simple Project** - Isolate complex model issues

### **No Data Loading**
1. **Verify MEP Project** - Check cable tray modeling
2. **Check Categories** - Ensure proper element categories
3. **Verify Circuits** - Check electrical system definitions
4. **Test Manual Selection** - Verify elements are selectable

### **Parameter Issues**
1. **Check Parameter Names** - Must match exactly
2. **Verify Parameter Types** - Text parameters required
3. **Check Category Assignment** - Must be on cable trays
4. **Test Parameter Access** - Verify read/write permissions

---

## 📞 **Support and Maintenance**

### **Getting Help**
1. **Check Documentation** - User Guide and Cheat Sheet
2. **Review Error Messages** - Note exact text
3. **Test with Sample Project** - Isolate project-specific issues
4. **Contact Support** with detailed information

### **Regular Maintenance**
1. **Update Plugin** when new versions available
2. **Backup Project Parameters** before major changes
3. **Monitor Performance** with large projects
4. **Update Documentation** for team members

### **Best Practices**
1. **Train Team Members** on proper usage
2. **Establish Workflows** for consistent results
3. **Regular Testing** with new project types
4. **Feedback Collection** for improvements

---

## ✅ **Installation Complete**

After successful installation and setup:

1. **✅ Plugin Available** - Traylor Swift button in Add-ins tab
2. **✅ Project Configured** - Parameters and families loaded
3. **✅ Functionality Tested** - All features working
4. **✅ Team Trained** - Users understand workflow
5. **✅ Standards Established** - Consistent usage patterns

**Your Traylor Swift installation is ready for production use!** 🎉

---

## 📋 **Quick Setup Checklist**

### **Installation**
- [ ] Plugin files copied to Revit Add-ins folder
- [ ] Revit restarted
- [ ] Traylor Swift button appears in Add-ins tab
- [ ] Plugin launches without errors

### **Project Setup**
- [ ] MEP project with cable trays
- [ ] TS_* parameters added to cable tray families
- [ ] Cable tray tag family loaded
- [ ] BecaParametricCable family loaded
- [ ] Electrical circuits defined

### **Testing**
- [ ] Data loads successfully
- [ ] Cable detection works
- [ ] Tagging functions in floor plan
- [ ] Section view creation works
- [ ] Parametric cable creation works
- [ ] Cable viewer displays data

**Ready to use Traylor Swift for efficient cable tray documentation!** 🚀
