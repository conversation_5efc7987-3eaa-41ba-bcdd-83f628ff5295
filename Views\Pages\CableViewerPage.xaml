﻿<Page x:Class="MEP.TraylorSwift.Views.Pages.CableViewerPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.TraylorSwift.Views.Pages"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="CableViewerPage">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10" Orientation="Horizontal">
            <Button
                Width="40"
                Height="40"
                Margin="0,0,10,0"
                Command="{Binding NavigateBackCommand}"
                ToolTip="Back to Tray Segments">
                <materialDesign:PackIcon Kind="ArrowLeft" />
            </Button>
            <TextBlock
                VerticalAlignment="Center"
                Text="Cable Viewer" />
        </StackPanel>

        <!-- Search and Filter -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBox
                    Grid.Column="0"
                    materialDesign:HintAssist.Hint="Search cables..."
                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                <Button
                    Grid.Column="2"
                    Command="{Binding ClearSearchCommand}"
                    Content="Clear" />
            </Grid>
        </materialDesign:Card>

        <!-- Cables DataGrid -->
        <materialDesign:Card Grid.Row="2" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Margin="0,0,0,10" Orientation="Horizontal">
                    <TextBlock Text="All Cables" />
                    <TextBlock
                        Margin="10,0,0,0"
                        VerticalAlignment="Center"
                        Text="{Binding CablesView.Count, StringFormat='({0} cables)'}" />
                </StackPanel>

                <DataGrid
                    Grid.Row="1"
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    ItemsSource="{Binding CablesView}"
                    SelectedItem="{Binding SelectedCable}"
                    SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn
                            Width="120"
                            Binding="{Binding CableRef}"
                            Header="Cable Ref." />
                        <DataGridTextColumn
                            Width="150"
                            Binding="{Binding Name}"
                            Header="Name" />
                        <DataGridTextColumn
                            Width="120"
                            Binding="{Binding GetToEquipmentName}"
                            Header="To" />
                        <DataGridTextColumn
                            Width="120"
                            Binding="{Binding GetFromEquipmentName}"
                            Header="From" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Diameter, StringFormat=F2}"
                            Header="Diameter" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Weight, StringFormat=F2}"
                            Header="Weight" />
                        <DataGridTemplateColumn Width="120" Header="Actions">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30"
                                        Command="{Binding DataContext.ShowInRevitCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="Show in Revit"
                                        FontSize="10" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
