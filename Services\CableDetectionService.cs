using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of ICableDetectionService for detecting and analyzing cables in relation to cable trays
    /// </summary>
    public class CableDetectionService : ICableDetectionService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private readonly ISpatialIndexService _spatialIndexService;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the cable detection service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        /// <param name="spatialIndexService">Spatial index service for efficient queries</param>
        public CableDetectionService(Document document, BecaActivityLoggerData logger, ISpatialIndexService spatialIndexService)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
            _spatialIndexService = spatialIndexService ?? throw new ArgumentNullException(nameof(spatialIndexService));
        }

        #endregion

        #region Cable Discovery

        /// <summary>
        /// Find all electrical circuits in the document
        /// </summary>
        public List<Element> FindAllElectricalCircuits()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_ElectricalCircuit)
                    .WhereElementIsNotElementType();

                var circuits = collector.ToList();
                _logger?.Log($"Found {circuits.Count} electrical circuits in document", LogType.Information);
                return circuits;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find electrical circuits: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        /// <summary>
        /// Find all electrical equipment in the document
        /// </summary>
        public List<Element> FindAllElectricalEquipment()
        {
            try
            {
                var categories = new List<BuiltInCategory>
                {
                    BuiltInCategory.OST_ElectricalEquipment,
                    BuiltInCategory.OST_ElectricalFixtures,
                    BuiltInCategory.OST_LightingFixtures,
                    BuiltInCategory.OST_FireAlarmDevices,
                    BuiltInCategory.OST_CommunicationDevices,
                    BuiltInCategory.OST_SecurityDevices,
                    BuiltInCategory.OST_NurseCallDevices,
                    BuiltInCategory.OST_DataDevices,
                    BuiltInCategory.OST_TelephoneDevices
                };

                var allEquipment = new List<Element>();

                foreach (var category in categories)
                {
                    try
                    {
                        var collector = new FilteredElementCollector(_document)
                            .OfCategory(category)
                            .WhereElementIsNotElementType();

                        allEquipment.AddRange(collector.ToList());
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to collect equipment from category {category}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Found {allEquipment.Count} electrical equipment elements", LogType.Information);
                return allEquipment;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find electrical equipment: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        /// <summary>
        /// Create cable model from circuit information
        /// </summary>
        public CableModel CreateCableModelFromCircuit(Element circuit)
        {
            try
            {
                if (circuit == null) return null;

                var cable = new CableModel();

                // Extract basic circuit information
                cable.CableRef = GetCircuitNumber(circuit);
                cable.Name = circuit.Name ?? cable.CableRef;

                // Get connected equipment
                var connectedElements = GetCircuitConnectedElements(circuit);
                if (connectedElements.Count >= 2)
                {
                    cable.From = connectedElements.First();
                    cable.To = connectedElements.Last();
                }

                // Extract cable properties
                cable.Diameter = CalculateCableDiameter(circuit);
                cable.Weight = CalculateCableWeight(circuit, 0); // Length will be calculated later
                cable.CableType = DetermineCableType(circuit).ToString();
                cable.VoltageRating = GetCircuitVoltage(circuit);
                cable.CurrentCapacity = GetCircuitCurrent(circuit);

                // Get circuit path geometry
                cable.RoutePoints = GetCircuitPathGeometry(circuit);

                _logger?.Log($"Created cable model for circuit {cable.CableRef}", LogType.Information);
                return cable;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create cable model from circuit: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Get all cables from circuits in the document
        /// </summary>
        public Dictionary<string, CableModel> GetAllCablesFromCircuits()
        {
            var cables = new Dictionary<string, CableModel>();

            try
            {
                var circuits = FindAllElectricalCircuits();

                foreach (var circuit in circuits)
                {
                    try
                    {
                        var cable = CreateCableModelFromCircuit(circuit);
                        if (cable != null && !string.IsNullOrEmpty(cable.CableRef))
                        {
                            cables[cable.CableRef] = cable;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to process circuit {circuit.Id}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Created {cables.Count} cable models from circuits", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get cables from circuits: {ex.Message}", LogType.Error);
            }

            return cables;
        }

        #endregion

        #region Circuit Path Analysis

        /// <summary>
        /// Get circuit path geometry from electrical circuit
        /// </summary>
        public List<XYZ> GetCircuitPathGeometry(Element circuit)
        {
            var pathPoints = new List<XYZ>();

            try
            {
                if (circuit == null) return pathPoints;

                // Try to get circuit path from ElectricalSystem
                if (circuit is ElectricalSystem electricalSystem)
                {
                    // Get circuit path points from the electrical system
                    var connectedElements = GetCircuitConnectedElements(circuit);
                    
                    if (connectedElements.Count >= 2)
                    {
                        // Simple path: connect equipment locations
                        foreach (var element in connectedElements)
                        {
                            var location = GetElementLocation(element);
                            if (location != null)
                            {
                                pathPoints.Add(location);
                            }
                        }
                    }
                }

                // If no path found, try to extract from circuit geometry
                if (pathPoints.Count == 0)
                {
                    pathPoints = ExtractPathFromCircuitGeometry(circuit);
                }

                _logger?.Log($"Extracted {pathPoints.Count} path points from circuit", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get circuit path geometry: {ex.Message}", LogType.Error);
            }

            return pathPoints;
        }

        /// <summary>
        /// Get circuit path curves from electrical circuit
        /// </summary>
        public List<Curve> GetCircuitPathCurves(Element circuit)
        {
            var curves = new List<Curve>();

            try
            {
                var pathPoints = GetCircuitPathGeometry(circuit);
                
                // Create line segments between consecutive points
                for (int i = 1; i < pathPoints.Count; i++)
                {
                    try
                    {
                        var line = Line.CreateBound(pathPoints[i - 1], pathPoints[i]);
                        curves.Add(line);
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to create curve segment: {ex.Message}", LogType.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get circuit path curves: {ex.Message}", LogType.Error);
            }

            return curves;
        }

        /// <summary>
        /// Extract cable routing information from circuit path
        /// </summary>
        public CableRoutingInfo ExtractCableRoutingInfo(Element circuit)
        {
            var routingInfo = new CableRoutingInfo();

            try
            {
                if (circuit == null) return routingInfo;

                routingInfo.RoutePoints = GetCircuitPathGeometry(circuit);
                routingInfo.RouteCurves = GetCircuitPathCurves(circuit);

                // Calculate total length
                routingInfo.TotalLength = routingInfo.RouteCurves.Sum(curve => curve.Length);

                // Calculate bounding box
                if (routingInfo.RoutePoints.Count > 0)
                {
                    var min = new XYZ(
                        routingInfo.RoutePoints.Min(p => p.X),
                        routingInfo.RoutePoints.Min(p => p.Y),
                        routingInfo.RoutePoints.Min(p => p.Z)
                    );
                    var max = new XYZ(
                        routingInfo.RoutePoints.Max(p => p.X),
                        routingInfo.RoutePoints.Max(p => p.Y),
                        routingInfo.RoutePoints.Max(p => p.Z)
                    );

                    routingInfo.RouteBoundingBox = new BoundingBoxXYZ { Min = min, Max = max };
                }

                // Get connected equipment
                var connectedElements = GetCircuitConnectedElements(circuit);
                if (connectedElements.Count >= 2)
                {
                    routingInfo.FromEquipment = connectedElements.First();
                    routingInfo.ToEquipment = connectedElements.Last();
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to extract cable routing info: {ex.Message}", LogType.Error);
            }

            return routingInfo;
        }

        #endregion

        #region Geometric Intersection

        /// <summary>
        /// Find cables that intersect with a cable tray using geometric analysis
        /// </summary>
        public List<CableModel> FindCablesInTray(CableTray cableTray, Dictionary<string, CableModel> availableCables, double tolerance = 0.1)
        {
            var intersectingCables = new List<CableModel>();

            try
            {
                if (cableTray == null || availableCables == null) return intersectingCables;

                var trayBoundingBox = cableTray.get_BoundingBox(null);
                if (trayBoundingBox == null) return intersectingCables;

                // Expand tray bounding box by tolerance
                trayBoundingBox.Min -= new XYZ(tolerance, tolerance, tolerance);
                trayBoundingBox.Max += new XYZ(tolerance, tolerance, tolerance);

                foreach (var cable in availableCables.Values)
                {
                    if (DoesCableIntersectTray(cable, cableTray, tolerance))
                    {
                        intersectingCables.Add(cable);
                    }
                }

                _logger?.Log($"Found {intersectingCables.Count} cables intersecting tray {cableTray.Id}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cables in tray: {ex.Message}", LogType.Error);
            }

            return intersectingCables;
        }

        /// <summary>
        /// Check if a cable intersects with a cable tray
        /// </summary>
        public bool DoesCableIntersectTray(CableModel cable, CableTray cableTray, double tolerance = 0.1)
        {
            try
            {
                if (cable?.RoutePoints == null || cable.RoutePoints.Count < 2 || cableTray == null)
                    return false;

                var trayBoundingBox = cableTray.get_BoundingBox(null);
                if (trayBoundingBox == null) return false;

                return cable.IntersectsWith(trayBoundingBox, tolerance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check cable-tray intersection: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find cables near a cable tray within a specified distance
        /// </summary>
        public List<CableModel> FindCablesNearTray(CableTray cableTray, Dictionary<string, CableModel> availableCables, double maxDistance = 1.0)
        {
            var nearbyCables = new List<CableModel>();

            try
            {
                if (cableTray == null || availableCables == null) return nearbyCables;

                var trayBoundingBox = cableTray.get_BoundingBox(null);
                if (trayBoundingBox == null) return nearbyCables;

                // Expand bounding box by max distance
                trayBoundingBox.Min -= new XYZ(maxDistance, maxDistance, maxDistance);
                trayBoundingBox.Max += new XYZ(maxDistance, maxDistance, maxDistance);

                foreach (var cable in availableCables.Values)
                {
                    if (cable.IntersectsWith(trayBoundingBox, maxDistance))
                    {
                        nearbyCables.Add(cable);
                    }
                }

                _logger?.Log($"Found {nearbyCables.Count} cables near tray {cableTray.Id}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cables near tray: {ex.Message}", LogType.Error);
            }

            return nearbyCables;
        }

        #endregion

        #region Circuit Path Intersection

        /// <summary>
        /// Find cables by analyzing circuit path intersection with cable trays
        /// </summary>
        public List<CableModel> FindCablesByCircuitPathIntersection(CableTray cableTray, List<Element> circuits, double tolerance = 0.1)
        {
            var intersectingCables = new List<CableModel>();

            try
            {
                if (cableTray == null || circuits == null) return intersectingCables;

                foreach (var circuit in circuits)
                {
                    if (DoesCircuitPathIntersectTray(circuit, cableTray, tolerance))
                    {
                        var cable = CreateCableModelFromCircuit(circuit);
                        if (cable != null)
                        {
                            intersectingCables.Add(cable);
                        }
                    }
                }

                _logger?.Log($"Found {intersectingCables.Count} cables by circuit path intersection", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cables by circuit path intersection: {ex.Message}", LogType.Error);
            }

            return intersectingCables;
        }

        /// <summary>
        /// Check if circuit path intersects with cable tray
        /// </summary>
        public bool DoesCircuitPathIntersectTray(Element circuit, CableTray cableTray, double tolerance = 0.1)
        {
            try
            {
                if (circuit == null || cableTray == null) return false;

                var pathPoints = GetCircuitPathGeometry(circuit);
                if (pathPoints.Count < 2) return false;

                var trayBoundingBox = cableTray.get_BoundingBox(null);
                if (trayBoundingBox == null) return false;

                // Check if any path segment intersects the tray bounding box
                for (int i = 1; i < pathPoints.Count; i++)
                {
                    if (LineIntersectsBoundingBox(pathPoints[i - 1], pathPoints[i], trayBoundingBox, tolerance))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check circuit path intersection: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Connectivity Analysis

        /// <summary>
        /// Find cables connected to specific electrical equipment
        /// </summary>
        public List<CableModel> FindCablesConnectedToEquipment(Element equipment)
        {
            var connectedCables = new List<CableModel>();

            try
            {
                if (equipment == null) return connectedCables;

                var circuits = FindAllElectricalCircuits();

                foreach (var circuit in circuits)
                {
                    var connectedElements = GetCircuitConnectedElements(circuit);
                    if (connectedElements.Any(e => e.Id == equipment.Id))
                    {
                        var cable = CreateCableModelFromCircuit(circuit);
                        if (cable != null)
                        {
                            connectedCables.Add(cable);
                        }
                    }
                }

                _logger?.Log($"Found {connectedCables.Count} cables connected to equipment {equipment.Id}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cables connected to equipment: {ex.Message}", LogType.Error);
            }

            return connectedCables;
        }

        /// <summary>
        /// Analyze connectivity between electrical equipment through cable trays
        /// </summary>
        public List<CableTray> AnalyzeEquipmentConnectivity(Element fromEquipment, Element toEquipment, List<CableTray> cableTrays)
        {
            var connectionPath = new List<CableTray>();

            try
            {
                if (fromEquipment == null || toEquipment == null || cableTrays == null)
                    return connectionPath;

                // This is a simplified implementation
                // In a full implementation, we would use pathfinding algorithms
                var fromLocation = GetElementLocation(fromEquipment);
                var toLocation = GetElementLocation(toEquipment);

                if (fromLocation == null || toLocation == null) return connectionPath;

                // Find trays that could form a path between the equipment
                var candidateTrays = cableTrays.Where(tray =>
                {
                    var trayBBox = tray.get_BoundingBox(null);
                    if (trayBBox == null) return false;

                    var trayCenter = (trayBBox.Min + trayBBox.Max) * 0.5;
                    var distanceToFrom = trayCenter.DistanceTo(fromLocation);
                    var distanceTo = trayCenter.DistanceTo(toLocation);

                    // Simple heuristic: tray should be reasonably close to both equipment
                    return distanceToFrom < 50.0 && distanceTo < 50.0;
                }).ToList();

                connectionPath.AddRange(candidateTrays);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to analyze equipment connectivity: {ex.Message}", LogType.Error);
            }

            return connectionPath;
        }

        /// <summary>
        /// Find the shortest path between two points through cable trays
        /// </summary>
        public List<CableTray> FindShortestTrayPath(XYZ startPoint, XYZ endPoint, List<CableTray> cableTrays)
        {
            var shortestPath = new List<CableTray>();

            try
            {
                if (startPoint == null || endPoint == null || cableTrays == null)
                    return shortestPath;

                // Simplified implementation - in reality we would use A* or Dijkstra's algorithm
                var sortedTrays = cableTrays
                    .Select(tray => new
                    {
                        Tray = tray,
                        Distance = CalculateDistanceToPath(tray, startPoint, endPoint)
                    })
                    .OrderBy(x => x.Distance)
                    .Take(10) // Limit to reasonable number
                    .Select(x => x.Tray)
                    .ToList();

                shortestPath.AddRange(sortedTrays);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find shortest tray path: {ex.Message}", LogType.Error);
            }

            return shortestPath;
        }

        #endregion

        #region Cable Properties

        /// <summary>
        /// Extract cable properties from circuit parameters
        /// </summary>
        public Dictionary<string, object> ExtractCableProperties(Element circuit)
        {
            var properties = new Dictionary<string, object>();

            try
            {
                if (circuit == null) return properties;

                // Extract common circuit parameters
                properties["CircuitNumber"] = GetCircuitNumber(circuit);
                properties["Voltage"] = GetCircuitVoltage(circuit);
                properties["Current"] = GetCircuitCurrent(circuit);
                properties["Power"] = GetCircuitPower(circuit);
                properties["LoadName"] = circuit.get_Parameter(BuiltInParameter.RBS_WIRE_CIRCUIT_LOAD_NAME)?.AsString();
                properties["Panel"] = circuit.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_PANEL_PARAM)?.AsString();
                properties["WireSize"] = GetWireSize(circuit);
                properties["WireType"] = GetWireType(circuit);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to extract cable properties: {ex.Message}", LogType.Error);
            }

            return properties;
        }

        /// <summary>
        /// Calculate cable diameter from circuit properties
        /// </summary>
        public double CalculateCableDiameter(Element circuit)
        {
            try
            {
                if (circuit == null) return 0.0;

                // Try to get wire size parameter
                var wireSize = GetWireSize(circuit);
                if (!string.IsNullOrEmpty(wireSize))
                {
                    // Convert wire size to diameter (simplified calculation)
                    // In reality, we would use proper wire gauge to diameter conversion tables
                    if (double.TryParse(wireSize.Replace("AWG", "").Replace("#", "").Trim(), out double gauge))
                    {
                        // Simplified formula for AWG to diameter conversion (in inches)
                        var diameterInches = 0.005 * Math.Pow(92, (36 - gauge) / 39.0);
                        return diameterInches * 12.0; // Convert to feet (Revit units)
                    }
                }

                // Default diameter based on circuit current
                var current = GetCircuitCurrent(circuit);
                if (current > 100) return 1.0 / 12.0; // 1 inch
                if (current > 50) return 0.75 / 12.0; // 3/4 inch
                if (current > 20) return 0.5 / 12.0; // 1/2 inch
                return 0.375 / 12.0; // 3/8 inch
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate cable diameter: {ex.Message}", LogType.Error);
                return 0.5 / 12.0; // Default 1/2 inch
            }
        }

        /// <summary>
        /// Calculate cable weight from circuit properties
        /// </summary>
        public double CalculateCableWeight(Element circuit, double length)
        {
            try
            {
                if (circuit == null || length <= 0) return 0.0;

                var diameter = CalculateCableDiameter(circuit);
                var wireType = GetWireType(circuit);

                // Simplified weight calculation (pounds per foot)
                double weightPerFoot = 0.1; // Default

                if (wireType?.ToLowerInvariant().Contains("copper") == true)
                {
                    weightPerFoot = Math.PI * Math.Pow(diameter / 2.0, 2) * 559.0; // Copper density
                }
                else if (wireType?.ToLowerInvariant().Contains("aluminum") == true)
                {
                    weightPerFoot = Math.PI * Math.Pow(diameter / 2.0, 2) * 169.0; // Aluminum density
                }

                return weightPerFoot * length;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate cable weight: {ex.Message}", LogType.Error);
                return 0.0;
            }
        }

        /// <summary>
        /// Determine cable type from circuit properties
        /// </summary>
        public CableType DetermineCableType(Element circuit)
        {
            try
            {
                if (circuit == null) return CableType.Unknown;

                var loadName = circuit.get_Parameter(BuiltInParameter.RBS_WIRE_CIRCUIT_LOAD_NAME)?.AsString()?.ToLowerInvariant();
                var circuitType = circuit.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_TYPE)?.AsString()?.ToLowerInvariant();

                if (loadName?.Contains("lighting") == true || circuitType?.Contains("lighting") == true)
                    return CableType.Lighting;

                if (loadName?.Contains("power") == true || circuitType?.Contains("power") == true)
                    return CableType.Power;

                if (loadName?.Contains("control") == true || circuitType?.Contains("control") == true)
                    return CableType.Control;

                if (loadName?.Contains("data") == true || loadName?.Contains("communication") == true)
                    return CableType.Data;

                if (loadName?.Contains("fire") == true || loadName?.Contains("alarm") == true)
                    return CableType.FireAlarm;

                if (loadName?.Contains("instrument") == true)
                    return CableType.Instrumentation;

                return CableType.Power; // Default to power
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to determine cable type: {ex.Message}", LogType.Error);
                return CableType.Unknown;
            }
        }

        #endregion

        #region Validation and Quality Control

        /// <summary>
        /// Validate cable detection results
        /// </summary>
        public List<string> ValidateCableDetection(CableTray cableTray, List<CableModel> detectedCables)
        {
            var issues = new List<string>();

            try
            {
                if (cableTray == null)
                {
                    issues.Add("Cable tray is null");
                    return issues;
                }

                if (detectedCables == null)
                {
                    issues.Add("Detected cables list is null");
                    return issues;
                }

                // Check for reasonable number of cables
                if (detectedCables.Count > 100)
                {
                    issues.Add($"Unusually high number of cables detected: {detectedCables.Count}");
                }

                // Check for duplicate cable references
                var duplicates = detectedCables
                    .GroupBy(c => c.CableRef)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key);

                foreach (var duplicate in duplicates)
                {
                    issues.Add($"Duplicate cable reference detected: {duplicate}");
                }

                // Check for cables without valid routing
                var cablesWithoutRouting = detectedCables
                    .Where(c => c.RoutePoints == null || c.RoutePoints.Count < 2)
                    .Count();

                if (cablesWithoutRouting > 0)
                {
                    issues.Add($"{cablesWithoutRouting} cables detected without valid routing information");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Check for duplicate cable assignments
        /// </summary>
        public List<string> CheckForDuplicateCableAssignments(List<CableTraySegmentModel> allTraySegments)
        {
            var duplicates = new List<string>();

            try
            {
                if (allTraySegments == null) return duplicates;

                var cableAssignments = new Dictionary<string, List<string>>();

                foreach (var traySegment in allTraySegments)
                {
                    if (traySegment.Cables != null)
                    {
                        foreach (var cable in traySegment.Cables)
                        {
                            if (!string.IsNullOrEmpty(cable.CableRef))
                            {
                                if (!cableAssignments.ContainsKey(cable.CableRef))
                                {
                                    cableAssignments[cable.CableRef] = new List<string>();
                                }
                                cableAssignments[cable.CableRef].Add(traySegment.TrayRef);
                            }
                        }
                    }
                }

                foreach (var assignment in cableAssignments.Where(kvp => kvp.Value.Count > 1))
                {
                    duplicates.Add($"Cable {assignment.Key} assigned to multiple trays: {string.Join(", ", assignment.Value)}");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check for duplicate cable assignments: {ex.Message}", LogType.Error);
            }

            return duplicates;
        }

        /// <summary>
        /// Verify cable-tray associations are geometrically valid
        /// </summary>
        public bool VerifyCableTrayAssociations(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.Cables == null || traySegment.CableTray == null)
                    return false;

                var trayBoundingBox = traySegment.GetBoundingBox();
                if (trayBoundingBox == null) return false;

                // Check if all assigned cables actually intersect the tray
                foreach (var cable in traySegment.Cables)
                {
                    if (!cable.IntersectsWith(trayBoundingBox, 0.5)) // 0.5 ft tolerance
                    {
                        _logger?.Log($"Cable {cable.CableRef} does not intersect tray {traySegment.TrayRef}", LogType.Warning);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to verify cable-tray associations: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Advanced Analysis

        /// <summary>
        /// Perform multi-criteria cable detection combining different methods
        /// </summary>
        public List<CableDetectionResult> PerformMultiCriteriaCableDetection(
            CableTray cableTray,
            Dictionary<string, CableModel> availableCables,
            List<Element> circuits,
            Models.CableDetectionOptions analysisOptions)
        {
            var results = new List<CableDetectionResult>();

            try
            {
                if (cableTray == null || analysisOptions == null) return results;

                // Method 1: Geometric intersection
                if (analysisOptions.UseGeometricIntersection && availableCables != null)
                {
                    var geometricCables = FindCablesInTray(cableTray, availableCables, analysisOptions.IntersectionTolerance);
                    foreach (var cable in geometricCables)
                    {
                        results.Add(new CableDetectionResult
                        {
                            Cable = cable,
                            ConfidenceScore = 0.8,
                            AnalysisMethod = GeometricAnalysisType.Precise,
                            Reason = "Geometric intersection detected"
                        });
                    }
                }

                // Method 2: Circuit path analysis
                if (analysisOptions.UseCircuitPathAnalysis && circuits != null)
                {
                    var circuitCables = FindCablesByCircuitPathIntersection(cableTray, circuits, analysisOptions.IntersectionTolerance);
                    foreach (var cable in circuitCables)
                    {
                        var existing = results.FirstOrDefault(r => r.Cable.CableRef == cable.CableRef);
                        if (existing != null)
                        {
                            existing.ConfidenceScore = Math.Min(1.0, existing.ConfidenceScore + 0.3);
                            existing.Reason += "; Circuit path analysis confirmed";
                        }
                        else
                        {
                            results.Add(new CableDetectionResult
                            {
                                Cable = cable,
                                ConfidenceScore = 0.7,
                                AnalysisMethod = GeometricAnalysisType.CircuitPath,
                                Reason = "Circuit path intersection detected"
                            });
                        }
                    }
                }

                // Method 3: Proximity analysis
                if (analysisOptions.UseProximityAnalysis && availableCables != null)
                {
                    var proximityCables = FindCablesNearTray(cableTray, availableCables, analysisOptions.ProximityThreshold);
                    foreach (var cable in proximityCables)
                    {
                        var existing = results.FirstOrDefault(r => r.Cable.CableRef == cable.CableRef);
                        if (existing == null)
                        {
                            results.Add(new CableDetectionResult
                            {
                                Cable = cable,
                                ConfidenceScore = 0.4,
                                AnalysisMethod = GeometricAnalysisType.Proximity,
                                Reason = "Proximity analysis detected"
                            });
                        }
                    }
                }

                // Filter by minimum confidence score
                results = results
                    .Where(r => r.ConfidenceScore >= analysisOptions.MinimumConfidenceScore)
                    .OrderByDescending(r => r.ConfidenceScore)
                    .Take(analysisOptions.MaximumCablesPerTray)
                    .ToList();

                _logger?.Log($"Multi-criteria analysis found {results.Count} cable candidates", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to perform multi-criteria cable detection: {ex.Message}", LogType.Error);
            }

            return results;
        }

        /// <summary>
        /// Analyze cable routing efficiency through tray network
        /// </summary>
        public RoutingEfficiencyResult AnalyzeCableRoutingEfficiency(CableModel cable, List<CableTraySegmentModel> trayNetwork)
        {
            var result = new RoutingEfficiencyResult();

            try
            {
                if (cable?.RoutePoints == null || cable.RoutePoints.Count < 2 || trayNetwork == null)
                    return result;

                result.ActualLength = cable.CalculateRouteLength();

                // Calculate optimal length (straight line distance)
                if (cable.RoutePoints.Count >= 2)
                {
                    result.OptimalLength = cable.RoutePoints.First().DistanceTo(cable.RoutePoints.Last());
                }

                // Calculate efficiency score
                if (result.OptimalLength > 0)
                {
                    result.EfficiencyScore = result.OptimalLength / result.ActualLength;
                }

                // Generate improvement suggestions
                if (result.EfficiencyScore < 0.8)
                {
                    result.ImprovementSuggestions.Add("Consider more direct routing path");
                }

                if (result.ActualLength > result.OptimalLength * 1.5)
                {
                    result.ImprovementSuggestions.Add("Route appears significantly longer than optimal");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to analyze cable routing efficiency: {ex.Message}", LogType.Error);
            }

            return result;
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Get circuit number from circuit element
        /// </summary>
        private string GetCircuitNumber(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_NUMBER)?.AsString() ?? 
                       circuit?.Name ?? 
                       circuit?.Id.ToString();
            }
            catch
            {
                return circuit?.Id.ToString() ?? "Unknown";
            }
        }

        /// <summary>
        /// Get elements connected to a circuit
        /// </summary>
        private List<Element> GetCircuitConnectedElements(Element circuit)
        {
            var connectedElements = new List<Element>();

            try
            {
                if (circuit is ElectricalSystem electricalSystem)
                {
                    foreach (Element element in electricalSystem.Elements)
                    {
                        connectedElements.Add(element);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get circuit connected elements: {ex.Message}", LogType.Warning);
            }

            return connectedElements;
        }

        /// <summary>
        /// Get location of an element
        /// </summary>
        private XYZ GetElementLocation(Element element)
        {
            try
            {
                if (element?.Location is LocationPoint locationPoint)
                {
                    return locationPoint.Point;
                }
                else if (element?.Location is LocationCurve locationCurve)
                {
                    return locationCurve.Curve.Evaluate(0.5, true);
                }

                // Fallback to bounding box center
                var bbox = element?.get_BoundingBox(null);
                if (bbox != null)
                {
                    return (bbox.Min + bbox.Max) * 0.5;
                }
            }
            catch
            {
                // Ignore errors
            }

            return null;
        }

        /// <summary>
        /// Extract path from circuit geometry
        /// </summary>
        private List<XYZ> ExtractPathFromCircuitGeometry(Element circuit)
        {
            var pathPoints = new List<XYZ>();

            try
            {
                // This is a simplified implementation
                // In reality, we would need to extract the actual circuit path geometry
                var connectedElements = GetCircuitConnectedElements(circuit);
                
                foreach (var element in connectedElements)
                {
                    var location = GetElementLocation(element);
                    if (location != null)
                    {
                        pathPoints.Add(location);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to extract path from circuit geometry: {ex.Message}", LogType.Warning);
            }

            return pathPoints;
        }

        /// <summary>
        /// Get circuit voltage
        /// </summary>
        private double GetCircuitVoltage(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.RBS_ELEC_VOLTAGE)?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get circuit current
        /// </summary>
        private double GetCircuitCurrent(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.MEP_ANALYTICAL_ELEC_CURRENT)?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get circuit power
        /// </summary>
        private double GetCircuitPower(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.MEP_ANALYTICAL_ELEC_CURRENT)?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get wire size from circuit
        /// </summary>
        private string GetWireSize(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_WIRE_SIZE_PARAM)?.AsString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get wire type from circuit
        /// </summary>
        private string GetWireType(Element circuit)
        {
            try
            {
                return circuit?.get_Parameter(BuiltInParameter.RBS_ELEC_WIRE_TYPE)?.AsString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Check if line intersects bounding box
        /// </summary>
        private bool LineIntersectsBoundingBox(XYZ start, XYZ end, BoundingBoxXYZ bbox, double tolerance)
        {
            try
            {
                // Expand bounding box by tolerance
                var min = bbox.Min - new XYZ(tolerance, tolerance, tolerance);
                var max = bbox.Max + new XYZ(tolerance, tolerance, tolerance);

                // Simple line-box intersection test
                return (start.X >= min.X && start.X <= max.X && start.Y >= min.Y && start.Y <= max.Y && start.Z >= min.Z && start.Z <= max.Z) ||
                       (end.X >= min.X && end.X <= max.X && end.Y >= min.Y && end.Y <= max.Y && end.Z >= min.Z && end.Z <= max.Z);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculate distance from tray to path between two points
        /// </summary>
        private double CalculateDistanceToPath(CableTray tray, XYZ startPoint, XYZ endPoint)
        {
            try
            {
                var trayBBox = tray.get_BoundingBox(null);
                if (trayBBox == null) return double.MaxValue;

                var trayCenter = (trayBBox.Min + trayBBox.Max) * 0.5;
                
                // Calculate distance from tray center to line segment
                var lineVector = endPoint - startPoint;
                var pointVector = trayCenter - startPoint;
                
                var lineLength = lineVector.GetLength();
                if (lineLength < 1e-6) return trayCenter.DistanceTo(startPoint);
                
                var projection = pointVector.DotProduct(lineVector) / lineLength;
                projection = Math.Max(0, Math.Min(lineLength, projection));
                
                var closestPoint = startPoint + lineVector.Normalize() * projection;
                return trayCenter.DistanceTo(closestPoint);
            }
            catch
            {
                return double.MaxValue;
            }
        }

        #endregion
    }
}
