using Autodesk.Revit.DB;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Models
{
    /// <summary>
    /// Options for tagging operations
    /// </summary>
    public class TaggingOptions
    {
        /// <summary>
        /// Skip creating tags for trays that already have tags
        /// </summary>
        public bool SkipExistingTags { get; set; } = true;

        /// <summary>
        /// Automatically position tags to avoid overlaps
        /// </summary>
        public bool AvoidOverlaps { get; set; } = true;

        /// <summary>
        /// Radius for overlap avoidance in feet
        /// </summary>
        public double AvoidanceRadius { get; set; } = 3.0;

        /// <summary>
        /// Tag orientation preference
        /// </summary>
        public TagOrientation PreferredOrientation { get; set; } = TagOrientation.Horizontal;

        /// <summary>
        /// Include cable count in tag text
        /// </summary>
        public bool IncludeCableCount { get; set; } = true;

        /// <summary>
        /// Include capacity utilization in tag text
        /// </summary>
        public bool IncludeCapacity { get; set; } = true;

        /// <summary>
        /// Maximum number of cable references to show in tag
        /// </summary>
        public int MaxCableReferencesInTag { get; set; } = 5;
    }

    /// <summary>
    /// Options for tag positioning
    /// </summary>
    public class TagPositioningOptions
    {
        /// <summary>
        /// Whether to avoid overlaps with existing tags
        /// </summary>
        public bool AvoidOverlaps { get; set; } = true;

        /// <summary>
        /// Minimum distance between tags in feet
        /// </summary>
        public double AvoidanceRadius { get; set; } = 3.0;

        /// <summary>
        /// Preferred vertical offset from tray in feet
        /// </summary>
        public double VerticalOffset { get; set; } = 2.0;

        /// <summary>
        /// Preferred horizontal offset from tray center in feet
        /// </summary>
        public double HorizontalOffset { get; set; } = 0.0;
    }

    /// <summary>
    /// Options for section view creation
    /// </summary>
    public class SectionViewOptions
    {
        /// <summary>
        /// Skip creating section views for trays that already have them
        /// </summary>
        public bool SkipExistingViews { get; set; } = true;

        /// <summary>
        /// Prefix for section view names
        /// </summary>
        public string NamePrefix { get; set; } = "Section";

        /// <summary>
        /// Suffix for section view names
        /// </summary>
        public string NameSuffix { get; set; } = "";

        /// <summary>
        /// Default scale for section views
        /// </summary>
        public int DefaultScale { get; set; } = 48; // 1/4" = 1'-0"

        /// <summary>
        /// Width of section box in feet
        /// </summary>
        public double SectionWidth { get; set; } = 10.0;

        /// <summary>
        /// Height of section box in feet
        /// </summary>
        public double SectionHeight { get; set; } = 8.0;

        /// <summary>
        /// Depth of section box in feet
        /// </summary>
        public double SectionDepth { get; set; } = 2.0;

        /// <summary>
        /// Whether to show crop box in section views
        /// </summary>
        public bool ShowCropBox { get; set; } = true;

        /// <summary>
        /// View template to apply (if any)
        /// </summary>
        public ElementId ViewTemplateId { get; set; } = ElementId.InvalidElementId;
    }

    /// <summary>
    /// Configuration for individual section view
    /// </summary>
    public class SectionViewConfiguration
    {
        /// <summary>
        /// Name for the section view
        /// </summary>
        public string ViewName { get; set; }

        /// <summary>
        /// Scale for the section view
        /// </summary>
        public int Scale { get; set; } = 48;

        /// <summary>
        /// Whether to show crop box
        /// </summary>
        public bool? CropBoxVisible { get; set; }

        /// <summary>
        /// View template to apply
        /// </summary>
        public ElementId ViewTemplateId { get; set; } = ElementId.InvalidElementId;

        /// <summary>
        /// Whether to add dimensions to the section
        /// </summary>
        public bool AddDimensions { get; set; } = false;

        /// <summary>
        /// Whether to add labels to the section
        /// </summary>
        public bool AddLabels { get; set; } = true;

        /// <summary>
        /// Custom section box dimensions
        /// </summary>
        public double? CustomWidth { get; set; }

        /// <summary>
        /// Custom section box height
        /// </summary>
        public double? CustomHeight { get; set; }

        /// <summary>
        /// Custom section box depth
        /// </summary>
        public double? CustomDepth { get; set; }
    }

    /// <summary>
    /// Options for parametric cable creation
    /// </summary>
    public class ParametricCableOptions
    {
        /// <summary>
        /// Skip creating parametric cables for trays that already have them
        /// </summary>
        public bool SkipExistingCables { get; set; } = true;

        /// <summary>
        /// Default cable family to use
        /// </summary>
        public string ParametricCableFamilyName { get; set; } = "BecaParametricCable";

        /// <summary>
        /// Whether to show individual cables
        /// </summary>
        public bool ShowIndividualCables { get; set; } = true;

        /// <summary>
        /// Whether to show cable labels
        /// </summary>
        public bool ShowCableLabels { get; set; } = true;

        /// <summary>
        /// Default color scheme for cables
        /// </summary>
        public string DefaultColorScheme { get; set; } = "ByType";

        /// <summary>
        /// Cable transparency (0-100)
        /// </summary>
        public int CableTransparency { get; set; } = 20;

        /// <summary>
        /// Whether to show capacity fill visualization
        /// </summary>
        public bool ShowCapacityFill { get; set; } = true;
    }

    /// <summary>
    /// Configuration for individual parametric cable
    /// </summary>
    public class ParametricCableConfiguration
    {
        /// <summary>
        /// Number of cables to represent
        /// </summary>
        public int? CableCount { get; set; }

        /// <summary>
        /// Cable diameter in inches
        /// </summary>
        public double? CableDiameter { get; set; }

        /// <summary>
        /// Cable type designation
        /// </summary>
        public string CableType { get; set; }

        /// <summary>
        /// Color scheme for the cable
        /// </summary>
        public string ColorScheme { get; set; }

        /// <summary>
        /// Cable material
        /// </summary>
        public string CableMaterial { get; set; }

        /// <summary>
        /// Cable insulation type
        /// </summary>
        public string InsulationType { get; set; }

        /// <summary>
        /// Voltage rating
        /// </summary>
        public string VoltageRating { get; set; }

        /// <summary>
        /// Whether to show cable as bundle or individual wires
        /// </summary>
        public bool ShowAsBundle { get; set; } = true;

        /// <summary>
        /// Custom cable length (if different from tray length)
        /// </summary>
        public double? CustomLength { get; set; }
    }

    /// <summary>
    /// Options for cable visibility and display
    /// </summary>
    public class CableVisibilityOptions
    {
        /// <summary>
        /// Whether to show individual cables
        /// </summary>
        public bool ShowIndividualCables { get; set; } = true;

        /// <summary>
        /// Whether to show cable labels
        /// </summary>
        public bool ShowCableLabels { get; set; } = true;

        /// <summary>
        /// Whether to show capacity fill visualization
        /// </summary>
        public bool ShowCapacityFill { get; set; } = true;

        /// <summary>
        /// Cable transparency (0-100)
        /// </summary>
        public int CableTransparency { get; set; } = 20;

        /// <summary>
        /// Color scheme for cables
        /// </summary>
        public string ColorScheme { get; set; } = "ByType";

        /// <summary>
        /// Whether to show cable direction arrows
        /// </summary>
        public bool ShowDirectionArrows { get; set; } = false;

        /// <summary>
        /// Whether to show cable tags
        /// </summary>
        public bool ShowCableTags { get; set; } = false;

        /// <summary>
        /// Line weight for cable display
        /// </summary>
        public int CableLineWeight { get; set; } = 1;

        /// <summary>
        /// Whether to use different colors for different cable types
        /// </summary>
        public bool ColorByType { get; set; } = true;

        /// <summary>
        /// Whether to use different colors for different voltage levels
        /// </summary>
        public bool ColorByVoltage { get; set; } = false;
    }

    /// <summary>
    /// Options for cable detection operations
    /// </summary>
    public class CableDetectionOptions
    {
        /// <summary>
        /// Use geometric intersection analysis
        /// </summary>
        public bool UseGeometricIntersection { get; set; } = true;

        /// <summary>
        /// Use circuit path analysis
        /// </summary>
        public bool UseCircuitPathAnalysis { get; set; } = true;

        /// <summary>
        /// Use proximity analysis
        /// </summary>
        public bool UseProximityAnalysis { get; set; } = true;

        /// <summary>
        /// Minimum confidence score for cable detection (0.0 - 1.0)
        /// </summary>
        public double MinimumConfidenceScore { get; set; } = 0.5;

        /// <summary>
        /// Maximum distance for proximity analysis in feet
        /// </summary>
        public double ProximityDistance { get; set; } = 2.0;

        /// <summary>
        /// Tolerance for geometric intersection in feet
        /// </summary>
        public double GeometricTolerance { get; set; } = 0.1;

        /// <summary>
        /// Tolerance for intersection detection in feet
        /// </summary>
        public double IntersectionTolerance { get; set; } = 0.1;

        /// <summary>
        /// Proximity threshold for cable detection in feet
        /// </summary>
        public double ProximityThreshold { get; set; } = 2.0;

        /// <summary>
        /// Maximum number of cables allowed per tray
        /// </summary>
        public int MaximumCablesPerTray { get; set; } = 100;

        /// <summary>
        /// Whether to include cables from connected equipment
        /// </summary>
        public bool IncludeConnectedEquipment { get; set; } = true;

        /// <summary>
        /// Whether to validate detected cables against circuit data
        /// </summary>
        public bool ValidateAgainstCircuits { get; set; } = true;
    }
}
