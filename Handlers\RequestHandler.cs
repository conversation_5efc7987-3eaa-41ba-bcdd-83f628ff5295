﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services;
using MEP.TraylorSwift.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.TraylorSwift.Handlers
{
    /// <summary>
    /// Request handler for Cable Tray Length
    /// Implements IExternalEventHandler to safely execute Revit API operations
    /// Uses dependency injection for clean service access
    /// </summary>
    public class RequestHandler : IExternalEventHandler
    {
        #region Fields

        private readonly IServiceProvider _serviceProvider;
        private readonly BecaActivityLoggerData _logger;
        private readonly RequestConfigure _request;

        #endregion

        #region Properties

        /// <summary>
        /// Get the request configuration object
        /// </summary>
        public RequestConfigure Request => _request;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the request handler with dependency injection
        /// </summary>
        /// <param name="serviceProvider">Service provider for accessing services</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public RequestHandler(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _request = new RequestConfigure();

            _logger?.Log("RequestHandler_PB6 initialized", LogType.Information);
        }

        #endregion

        #region IExternalEventHandler Implementation
        /// <summary>
        /// Get the name of this external event handler
        /// </summary>
        /// <returns>Handler name</returns>
        public string GetName()
        {
            return "Traylor Swift Request Handler";
        }

        /// <summary>
        /// Execute the pending request in the Revit API context
        /// This method is called by Revit when the ExternalEvent is raised
        /// </summary>
        /// <param name="uiapp">UI application from Revit</param>
        public void Execute(UIApplication app)
        {
            try
            {
                var requestId = _request.Take();
                _logger?.Log($"Executing request: {requestId}", LogType.Information);

                switch (requestId)
                {
                    case RequestId.None:
                        return;
                    case RequestId.AddTraySegment:
                        HandleAddTraySegment(app);
                        break;
                    case RequestId.RemoveTraySegment:
                        HandleRemoveTraySegment(app);
                        break;
                    case RequestId.CreateTraySection:
                        HandleCreateTraySection(app);
                        break;
                    case RequestId.UpdateParametricCable:
                        HandleUpdateParametricCable(app);
                        break;
                    case RequestId.ShowInRevit:
                        HandleShowInRevit(app);
                        break;
                    case RequestId.WriteTSCablesParameter:
                        HandleWriteTSCablesParameter(app);
                        break;
                    default:
                        break;
                }

            }
            catch (Exception ex)
            {
                _logger?.Log($"Request execution failed: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Always wake up the UI
                WakeUpMainWindow();
            }
        }

        #endregion

        #region Request Handler

        private void HandleAddTraySegment(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var uidoc = app.ActiveUIDocument;

                // Prompt user to select a cable tray
                var selection = uidoc.Selection;
                var reference = selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element,
                    new CableTraySelectionFilter(), "Select a cable tray to add:");

                if (reference != null)
                {
                    var element = doc.GetElement(reference);
                    if (element is CableTray cableTray && IsCableTray(cableTray))
                    {
                        // Check if active view is floor plan
                        var activeView = doc.ActiveView;
                        if (!(activeView is ViewPlan))
                        {
                            // Show floor plan required message with "don't show again" option
                            ShowFloorPlanRequiredMessage();
                            return;
                        }

                        // Store selected tray for processing by ViewModel
                        SharedData.SelectedTrayForProcessing = cableTray;
                        SharedData.ProcessingAction = "AddTraySegment";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle add tray segment: {ex.Message}", LogType.Error);
                System.Windows.MessageBox.Show($"Error selecting tray: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void HandleRemoveTraySegment(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var uidoc = app.ActiveUIDocument;

                // Prompt user to select a cable tray to remove
                var selection = uidoc.Selection;
                var reference = selection.PickObject(Autodesk.Revit.UI.Selection.ObjectType.Element,
                    new CableTraySelectionFilter(), "Select a cable tray to remove:");

                if (reference != null)
                {
                    var element = doc.GetElement(reference);
                    if (element is CableTray cableTray && IsCableTray(cableTray))
                    {
                        // Store selected tray for processing by ViewModel
                        SharedData.SelectedTrayForProcessing = cableTray;
                        SharedData.ProcessingAction = "RemoveTraySegment";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle remove tray segment: {ex.Message}", LogType.Error);
                System.Windows.MessageBox.Show($"Error selecting tray: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void HandleCreateTraySection(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var traySegment = SharedData.SelectedTraySegmentForProcessing;
                var sectionName = SharedData.SectionViewName;

                if (traySegment == null || string.IsNullOrWhiteSpace(sectionName))
                {
                    _logger?.Log("No tray segment or section name provided for section creation", LogType.Warning);
                    return;
                }

                using (var transaction = new Transaction(doc, "Create Tray Section"))
                {
                    transaction.Start();

                    try
                    {
                        // Create section view using the service
                        var serviceProvider = ServiceConfiguration.BuildServiceProvider(app.ActiveUIDocument, _logger);
                        var sectionViewService = serviceProvider.GetService<ISectionViewService>();

                        var sectionView = sectionViewService.CreateTraySectionView(traySegment, sectionName);

                        if (sectionView != null)
                        {
                            // Update tray segment
                            traySegment.SectionView = sectionView;

                            // Write to Revit parameter
                            var trayAnalysisService = serviceProvider.GetService<ITrayAnalysisService>();
                            trayAnalysisService?.WriteSectionViewParameter(traySegment.CableTray, sectionView);

                            transaction.Commit();
                            _logger?.Log($"Created section view: {sectionName}", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log("Failed to create section view", LogType.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Error creating section view: {ex.Message}", LogType.Error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle create tray section: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Clear shared data
                SharedData.SelectedTraySegmentForProcessing = null;
                SharedData.SectionViewName = null;
            }
        }

        private void HandleUpdateParametricCable(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var traySegment = SharedData.SelectedTraySegmentForProcessing;
                var config = SharedData.ParametricCableConfig;

                if (traySegment == null || config == null)
                {
                    _logger?.Log("No tray segment or configuration provided for parametric cable update", LogType.Warning);
                    return;
                }

                using (var transaction = new Transaction(doc, "Update Parametric Cable"))
                {
                    transaction.Start();

                    try
                    {
                        var serviceProvider = ServiceConfiguration.BuildServiceProvider(app.ActiveUIDocument, _logger);
                        var parametricCableService = serviceProvider.GetService<IParametricCableService>();
                        var trayAnalysisService = serviceProvider.GetService<ITrayAnalysisService>();

                        // Create or update parametric cable
                        FamilyInstance parametricCable = traySegment.BecaParametricCable;

                        if (parametricCable == null)
                        {
                            // Create new parametric cable
                            parametricCable = parametricCableService.CreateParametricCable(traySegment.CableTray, traySegment);
                            traySegment.BecaParametricCable = parametricCable;
                        }

                        if (parametricCable != null)
                        {
                            // Configure parameters (config parameter is optional)
                            var success = parametricCableService.ConfigureCableParameters(parametricCable, traySegment, null);

                            // TODO: In future, we could map the simple config properties to the complex config
                            // For now, we rely on the tray segment data for configuration

                            if (success)
                            {
                                // Write to Revit parameter
                                trayAnalysisService?.WriteParametricCableParameter(traySegment.CableTray, parametricCable);

                                transaction.Commit();
                                _logger?.Log("Updated parametric cable parameters", LogType.Information);
                            }
                            else
                            {
                                transaction.RollBack();
                                _logger?.Log("Failed to update parametric cable parameters", LogType.Error);
                            }
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log("Failed to create parametric cable", LogType.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Error updating parametric cable: {ex.Message}", LogType.Error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle update parametric cable: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Clear shared data
                SharedData.SelectedTraySegmentForProcessing = null;
                SharedData.ParametricCableConfig = null;
            }
        }

        private void HandleShowInRevit(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var uidoc = app.ActiveUIDocument;
                var cable = SharedData.SelectedCableForProcessing;

                if (cable == null)
                {
                    _logger?.Log("No cable provided for show in Revit", LogType.Warning);
                    return;
                }

                using (var transaction = new Transaction(doc, "Show Cable in 3D View"))
                {
                    transaction.Start();

                    try
                    {
                        // Create 3D view name
                        var userName = Environment.UserName;
                        var viewName = $"TS_3DView_{userName}";

                        // Get or create 3D view
                        var view3D = GetOrCreate3DView(doc, viewName);

                        if (view3D != null)
                        {
                            // Set section box to focus on cable route
                            SetSectionBoxForCable(view3D, cable);

                            // Configure visibility/graphics
                            ConfigureViewForCable(view3D, cable);

                            // Activate the view
                            uidoc.ActiveView = view3D;

                            // Zoom to fit
                            uidoc.RefreshActiveView();

                            transaction.Commit();
                            _logger?.Log($"Created/activated 3D view: {viewName} for cable {cable.CableRef}", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log("Failed to create 3D view", LogType.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Error showing cable in Revit: {ex.Message}", LogType.Error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle show in revit: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Clear shared data
                SharedData.SelectedCableForProcessing = null;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Check if element is a cable tray
        /// </summary>
        private bool IsCableTray(CableTray element)
        {
            return element?.Category?.Id?.IntegerValue == (int)BuiltInCategory.OST_CableTray;
        }

        /// <summary>
        /// Get or create a 3D view with the specified name
        /// </summary>
        private View3D GetOrCreate3DView(Document doc, string viewName)
        {
            try
            {
                // Try to find existing view
                var existing3DViews = new FilteredElementCollector(doc)
                    .OfClass(typeof(View3D))
                    .Cast<View3D>()
                    .Where(v => !v.IsTemplate && v.Name.Equals(viewName, StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault();

                if (existing3DViews != null)
                {
                    return existing3DViews;
                }

                // Create new 3D view
                var viewFamilyType = new FilteredElementCollector(doc)
                    .OfClass(typeof(ViewFamilyType))
                    .Cast<ViewFamilyType>()
                    .FirstOrDefault(vft => vft.ViewFamily == ViewFamily.ThreeDimensional);

                if (viewFamilyType != null)
                {
                    var view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
                    view3D.Name = viewName;
                    return view3D;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating 3D view: {ex.Message}", LogType.Error);
            }

            return null;
        }

        /// <summary>
        /// Set section box for cable route
        /// </summary>
        private void SetSectionBoxForCable(View3D view3D, CableModel cable)
        {
            try
            {
                if (cable.AssociatedTraySegment?.CableTray == null) return;

                var cableTray = cable.AssociatedTraySegment.CableTray;
                var bbox = cableTray.get_BoundingBox(null);

                if (bbox != null)
                {
                    // Expand bounding box for better view
                    var margin = 5.0; // 5 feet margin
                    var expandedBbox = new BoundingBoxXYZ
                    {
                        Min = new XYZ(bbox.Min.X - margin, bbox.Min.Y - margin, bbox.Min.Z - margin),
                        Max = new XYZ(bbox.Max.X + margin, bbox.Max.Y + margin, bbox.Max.Z + margin)
                    };

                    view3D.SetSectionBox(expandedBbox);
                    view3D.get_Parameter(BuiltInParameter.VIEWER_BOUND_ACTIVE_FAR).Set(1);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error setting section box: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Configure view visibility for cable
        /// </summary>
        private void ConfigureViewForCable(View3D view3D, CableModel cable)
        {
            try
            {
                // Hide most categories except cable trays and conduits
                var categoriesToShow = new[]
                {
                    BuiltInCategory.OST_CableTray,
                    BuiltInCategory.OST_Conduit,
                    BuiltInCategory.OST_ElectricalEquipment,
                    BuiltInCategory.OST_ElectricalFixtures
                };

                var doc = view3D.Document;
                var allCategories = doc.Settings.Categories;

                foreach (Category category in allCategories)
                {
                    if (category.get_AllowsVisibilityControl(view3D))
                    {
                        var shouldShow = categoriesToShow.Contains((BuiltInCategory)category.Id.IntegerValue);
                        category.set_Visible(view3D, shouldShow);
                    }
                }

                // Set visual style to shaded
                view3D.get_Parameter(BuiltInParameter.MODEL_GRAPHICS_STYLE).Set((int)DisplayStyle.Shading);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring view visibility: {ex.Message}", LogType.Warning);
            }
        }

        private void HandleWriteTSCablesParameter(UIApplication app)
        {
            try
            {
                var doc = app.ActiveUIDocument.Document;
                var traySegment = SharedData.SelectedTraySegmentForProcessing;

                if (traySegment?.CableTray == null)
                {
                    _logger?.Log("No tray segment provided for TS_Cables parameter writing", LogType.Warning);
                    return;
                }

                using (var transaction = new Transaction(doc, "Write TS_Cables Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Get the cable references from the tray segment
                        var cableRefs = traySegment.GetCablesParameterValue();

                        // Write the TS_Cables parameter to the tray element
                        var tsCablesParam = traySegment.CableTray.LookupParameter("TS_Cables");
                        if (tsCablesParam != null && !tsCablesParam.IsReadOnly)
                        {
                            tsCablesParam.Set(cableRefs);
                            transaction.Commit();
                            _logger?.Log($"Successfully wrote TS_Cables parameter for tray {traySegment.TrayRef}: '{cableRefs}'", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log($"TS_Cables parameter not found or read-only for tray {traySegment.TrayRef}", LogType.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Error writing TS_Cables parameter: {ex.Message}", LogType.Error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to handle write TS_Cables parameter: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Clear shared data
                SharedData.SelectedTraySegmentForProcessing = null;
            }
        }

        #endregion

        /// <summary>
        /// Wake up the main window after request processing
        /// </summary>
        private void WakeUpMainWindow()
        {
            try
            {
                ModelessMainWindowHandler.WakeUpMainWindow();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up main window: {ex.Message}", LogType.Error);
            }
        }

        #region Helper Methods

        /// <summary>
        /// Show floor plan required message with "don't show again" checkbox
        /// </summary>
        private void ShowFloorPlanRequiredMessage()
        {
            try
            {
                // Check if user has opted to not show this message
                var dontShowAgain = Properties.Settings.Default.DontShowFloorPlanMessage;
                if (dontShowAgain)
                {
                    return; // Don't show the message
                }

                // Create and show custom message box with checkbox
                var messageBoxWindow = new Views.FloorPlanRequiredMessageBox();
                var result = messageBoxWindow.ShowDialog();

                // Save the "don't show again" preference if checkbox was checked
                if (messageBoxWindow.DontShowAgain)
                {
                    Properties.Settings.Default.DontShowFloorPlanMessage = true;
                    Properties.Settings.Default.Save();
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to show floor plan message: {ex.Message}", LogType.Error);

                // Fallback to simple message box
                System.Windows.MessageBox.Show(
                    "Tags can only be created in Floor Plan views. Please switch to a Floor Plan view and try again.",
                    "Floor Plan Required",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
        }

        #endregion

    }
}
