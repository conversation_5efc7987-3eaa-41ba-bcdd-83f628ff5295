Product Name

**Traylor Swift**

Purpose

To streamline the process of tagging cable trays in Revit by identifying associated cables, visualizing tray data, and configuring parametric cable elements with intuitive UI controls and geometry-based logic.

**Flows and features:**

A. Tray Tagger feature:

1. On start the tool will check for every trays in the model and get the ones where 'TS_Cables' parameter is not null or empty and create a ‘CableTraySegmentModel’. The values in 'TS_Cables' parameter refers to the cable number separated by commas. These cable data will be stored in ‘CableModel’. 
2. Properties in CableTraySegmentModel will contain at least: FamilyInstance CableTray,  List<CableModel> Cables, string TrayRef, Level Level, double Width, int NumberOfCables, double Capacity (%), double Weight, FamilyInstance BecaParametricCable (to store a ‘BecaParametricCable’ Generic model), View SectionView (to store the Section View  of this tray). ‘BecaParametricCable’ property can be checked in the CableTray’s parameter called ‘TS_ParametricCable’, and ‘SectionView’ can be checked in the CableTray’s parameter called ‘TS_SectionView’
3. Properties in CableModel will contain at least: CableTraySegmentModel AssociatedTraySegment,  string CableRef, string Name, Element To (Electrical Equipment Element), Element From (Electrical Equipment Element), double Diameter, double Weight.
4. Create a ‘TS_Data’ class to store List<CableTraySegmentModel> CableTraySegments and ProjectInformation ProjectInfo, and other properties needed.
5. After retrieving TS_Data will open a modeless 'TrayTagger' window or page. This page will have at least these controls: DataGridView to show available TS_Data.CableTraySegments, DropDown of Levels to filter the DataGridView by Level with an item “All” to show all Levels,  and Buttons (’AddSegment’, ‘RemoveSegment’). 
6. Clicking AddSegment button will ask the user to select a tray (MEP.TraylorSwift.Handlers.RequestId.AddTraySegment) either from Revit’s 3D View or Floor Plan, however after selecting a tray the active view have to be a Floor Plan that is going to place a tag on it. Show a message box to inform this to the user and in that message box add a check box ‘don’t show this message again’ and ticking this check box will not show the message box again on the next AddSegment button click.
7. After the tray is selected and selection confirmed in Revit, the tool will run the following process.
8. Match cables: Based on the selected tray, this tool will then identify what cables goes on the selected cable tray by clashing the distribution board circuit path (blue line) with the trays, or by finding nearby cables from a certain threshold. This will need geometric calculation of the vertices of the tray against the line geometry of a cable or the circuit path (blue line in Revit’s Edit Circuit Path) to check if the cables are in the tray or should be in the tray.
9. Push cables info back into 'TS_Cables' parameter in the tray element showing the cable numbers separated by commas.
10. Create tag in active Floor Plan that will tag the selected tray showing info of the cables in the tray.
11. Add a Generic Model family that we have prepared called 'BecaParametricCable' on the tray that will be used in the next feature.
12. Add the selected tray in the DataGridView with these columns: 'Tray Ref', 'Width', 'Number of cables', 'Capacity (%)', 'Weight'.
13. The DataGridView should also have a Button column called 'Configure' (Clicking this button will navigate to 'Configure' page that will be explained in the next feature). 
14. Another Button column called ‘Remove Segment’ to remove the row, and also will remove the tag from the tray element, empty the 'TS_Cables' parameter, and remove BecaParametricCable FamilyInstance that’s on the tray (MEP.TraylorSwift.Handlers.RequestId.RemoveTraySegment).

B. Configure Tray Segment feature:

1. By clicking 'Configure' button in a row in the DataGridView in Tray Tagger page will open this page called 'Configure Tray Segment'. This page will show a DataGridView containing CableTraySegmentModel.CableModel data with these columns: 'Cable Ref.', 'Name', 'To (Electrical Equipment name)', 'From' (Electrical Equipment name), ' Diameter', 'Weight'. And if CableTraySegmentModel.SectionView exists it will also set the active view in Revit as the CableTraySegmentModel.SectionView
2. Under the DataGridView in this page there will be a TextBox for the [CableTraySegmentModel.SectionView.Name](CableTraySegmentModel.SectionView.Name) this TextBox will be ReadOnly = true if the [CableTraySegmentModel.SectionView](CableTraySegmentModel.SectionView.Name) exists or else ReadOnly = false
3.  Under the TextBox there’s a button called ‘Create Section’. This button will be disabled if CableTraySegmentModel.SectionView exists. Clicking this button if it’s enabled will check the TextBox.Text if it’s empty or the characters are invalid for Revit name show a message for the user and stop the process, or else create a Section View in Revit of the tray (MEP.TraylorSwift.Handlers.RequestId.CreateTraySection) showing the tray and BecaParametricCable element and store this SectionView under CableTraySegmentModel.SectionView  
4. Under the  ‘Create Section’ button there’s a Border that contains controls to control the parameters of the BecaParametricCable element which are: TextBox for Minimum Space Capacity (%), Dropdown to select CabelArrangement [trefoil, flat], Dropdown to select CableSpacing [Touching, D, 2D], Button ‘Update Parameter’ will apply and update the parameters values of BecaParametricCable shown in the active SectionView (MEP.TraylorSwift.Handlers.RequestId.UpdateParametricCable). The controls in this Border will be disabled when ‘Create Section’ button is enabled.

C. Cable Viewer feature:

1. Another Page needed for this tool is called ‘Cable Viewer’. This page will show the data from all the CableModel available in the TS_Data and show them in a DataGridView with these columns: 'Cable Ref.', 'Name', 'To', 'From', ' Diameter', 'Weight', and a Button column called “Show in Revit”.
2. Clicking “Show in Revit” button will run the following process.
3. Create a 3D with the name “TS_3DView_<UserName> if it doesn’t exist. If the name exists use that view and clear it.
4. Get the cable from circuit path (blue line) geometry or vertices and Draw lines using those vertices representing the cable run.
5. Set the SectionBox of this 3D View to be the Min and Max of the cable run bounding box.
6. Show only these categories: Cable Trays, Cable Tray Fittings, Electrical Equipment and whichever categories used for creating the cable run but hide the elements of that category if they’re not the cable run. For the VisibilityGraphics set the cable run and associated cable trays and fittings in shaded and the other on associated  cable trays, fittings, and electrical equipment with low opacity.

Technical:

1. Use MVVM and DI architecture
2. Use modeless WPF
3. Avoid any Async Task in the code
4. Use Spatial Indexing for efficient and effective geometric lookup
5. Some elements might not properly connected using MEP Connector (bad modeling) like the tray appears to be touching but not connected using MEP Connector, therefore consider the adjacency and closeness between elements to define connectivity.