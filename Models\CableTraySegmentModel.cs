using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using View = Autodesk.Revit.DB.View;

namespace MEP.TraylorSwift.Models
{
    /// <summary>
    /// Represents a cable tray segment with its associated cables and properties
    /// </summary>
    public class CableTraySegmentModel
    {
        #region Properties

        /// <summary>
        /// The Revit cable tray 
        /// </summary>
        public CableTray CableTray { get; set; }

        /// <summary>
        /// ObservableCollection of cables contained in this tray segment
        /// </summary>
        public ObservableCollection<CableModel> Cables { get; set; }

        /// <summary>
        /// Unique tray reference identifier
        /// </summary>
        public string TrayRef { get; set; }

        /// <summary>
        /// Level where this tray segment is located
        /// </summary>
        public Level Level { get; set; }

        /// <summary>
        /// Width of the cable tray in project units
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// Number of cables in this tray segment
        /// </summary>
        public int NumberOfCables => Cables?.Count ?? 0;

        /// <summary>
        /// Capacity utilization percentage (0-100)
        /// </summary>
        public double Capacity { get; set; }

        /// <summary>
        /// Total weight of the tray segment including cables
        /// </summary>
        public double Weight { get; set; }

        /// <summary>
        /// BecaParametricCable generic model family instance for visualization
        /// </summary>
        public FamilyInstance BecaParametricCable { get; set; }

        /// <summary>
        /// Section view associated with this tray segment
        /// </summary>
        public View SectionView { get; set; }

        /// <summary>
        /// Height of the cable tray in project units
        /// </summary>
        public double Height { get; set; }

        /// <summary>
        /// Length of the cable tray segment in project units
        /// </summary>
        public double Length { get; set; }

        /// <summary>
        /// Material of the cable tray
        /// </summary>
        public string Material { get; set; }

        /// <summary>
        /// Indicates if this tray segment is currently selected
        /// </summary>
        public bool IsSelected { get; set; }

        /// <summary>
        /// Indicates if this tray segment has been modified
        /// </summary>
        public bool IsModified { get; set; }

        /// <summary>
        /// Tag element associated with this tray segment
        /// </summary>
        public IndependentTag Tag { get; set; }

        /// <summary>
        /// Additional properties for extensibility
        /// </summary>
        public Dictionary<string, object> AdditionalProperties { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize a new cable tray segment model
        /// </summary>
        public CableTraySegmentModel()
        {
            Cables = new ObservableCollection<CableModel>();
            AdditionalProperties = new Dictionary<string, object>();
            TrayRef = string.Empty;
            Material = string.Empty;
        }

        /// <summary>
        /// Initialize a new cable tray segment model with a tray instance
        /// </summary>
        /// <param name="cableTray">Revit cable tray family instance</param>
        public CableTraySegmentModel(CableTray cableTray) : this()
        {
            CableTray = cableTray;
            if (cableTray != null)
            {
                InitializeFromTray(cableTray);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Add a cable to this tray segment
        /// </summary>
        /// <param name="cable">Cable to add</param>
        public void AddCable(CableModel cable)
        {
            if (cable == null) return;

            if (!Cables.Contains(cable))
            {
                Cables.Add(cable);
                cable.AssociatedTraySegment = this;
                IsModified = true;
                RecalculateProperties();
            }
        }

        /// <summary>
        /// Remove a cable from this tray segment
        /// </summary>
        /// <param name="cable">Cable to remove</param>
        /// <returns>True if cable was removed</returns>
        public bool RemoveCable(CableModel cable)
        {
            if (cable == null) return false;

            bool removed = Cables.Remove(cable);
            if (removed)
            {
                cable.AssociatedTraySegment = null;
                IsModified = true;
                RecalculateProperties();
            }
            return removed;
        }

        /// <summary>
        /// Remove a cable by reference
        /// </summary>
        /// <param name="cableRef">Cable reference to remove</param>
        /// <returns>True if cable was removed</returns>
        public bool RemoveCableByRef(string cableRef)
        {
            if (string.IsNullOrEmpty(cableRef)) return false;

            var cable = Cables.FirstOrDefault(c => string.Equals(c.CableRef, cableRef, StringComparison.OrdinalIgnoreCase));
            return cable != null && RemoveCable(cable);
        }

        /// <summary>
        /// Get cable by reference
        /// </summary>
        /// <param name="cableRef">Cable reference to find</param>
        /// <returns>Cable model or null if not found</returns>
        public CableModel GetCableByRef(string cableRef)
        {
            if (string.IsNullOrEmpty(cableRef)) return null;

            return Cables.FirstOrDefault(c => string.Equals(c.CableRef, cableRef, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Clear all cables from this tray segment
        /// </summary>
        public void ClearCables()
        {
            foreach (var cable in Cables)
            {
                cable.AssociatedTraySegment = null;
            }
            Cables.Clear();
            IsModified = true;
            RecalculateProperties();
        }

        /// <summary>
        /// Get the level name
        /// </summary>
        /// <returns>Level name or empty string if not available</returns>
        public string GetLevelName()
        {
            try
            {
                return Level?.Name ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Get the section view name
        /// </summary>
        /// <returns>Section view name or empty string if not available</returns>
        public string GetSectionViewName()
        {
            try
            {
                return SectionView?.Name ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Check if section view exists
        /// </summary>
        /// <returns>True if section view is available</returns>
        public bool HasSectionView()
        {
            return SectionView != null && SectionView.IsValidObject;
        }

        /// <summary>
        /// Check if parametric cable exists
        /// </summary>
        /// <returns>True if parametric cable is available</returns>
        public bool HasParametricCable()
        {
            return BecaParametricCable != null && BecaParametricCable.IsValidObject;
        }

        /// <summary>
        /// Get the bounding box of the tray segment
        /// </summary>
        /// <returns>Bounding box or null if tray is not available</returns>
        public BoundingBoxXYZ GetBoundingBox()
        {
            try
            {
                return CableTray?.get_BoundingBox(null);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get the center point of the tray segment
        /// </summary>
        /// <returns>Center point or null if not available</returns>
        public XYZ GetCenterPoint()
        {
            var bbox = GetBoundingBox();
            if (bbox == null) return null;

            return (bbox.Min + bbox.Max) * 0.5;
        }

        /// <summary>
        /// Calculate total cable weight
        /// </summary>
        /// <returns>Total weight of all cables</returns>
        public double CalculateTotalCableWeight()
        {
            return Cables?.Sum(c => c.Weight) ?? 0.0;
        }

        /// <summary>
        /// Calculate capacity utilization based on cable cross-sectional areas
        /// </summary>
        /// <returns>Capacity percentage (0-100)</returns>
        public double CalculateCapacityUtilization()
        {
            if (Width <= 0 || Height <= 0) return 0.0;

            double trayArea = Width * Height;
            double cableArea = Cables?.Sum(c => Math.PI * Math.Pow(c.Diameter / 2, 2)) ?? 0.0;

            return trayArea > 0 ? Math.Min(100.0, (cableArea / trayArea) * 100.0) : 0.0;
        }

        /// <summary>
        /// Get cables formatted as comma-separated string for TS_Cables parameter
        /// </summary>
        /// <returns>Comma-separated cable references</returns>
        public string GetCablesParameterValue()
        {
            if (Cables == null || Cables.Count == 0)
                return string.Empty;

            return string.Join(",", Cables.Select(c => c.CableRef).Where(r => !string.IsNullOrEmpty(r)));
        }

        /// <summary>
        /// Set cables from comma-separated parameter value
        /// </summary>
        /// <param name="parameterValue">Comma-separated cable references</param>
        /// <param name="availableCables">Dictionary of available cables by reference</param>
        public void SetCablesFromParameterValue(string parameterValue, Dictionary<string, CableModel> availableCables)
        {
            ClearCables();

            if (string.IsNullOrEmpty(parameterValue) || availableCables == null)
                return;

            var cableRefs = parameterValue.Split(',')
                .Select(r => r.Trim())
                .Where(r => !string.IsNullOrEmpty(r));

            foreach (var cableRef in cableRefs)
            {
                if (availableCables.TryGetValue(cableRef, out var cable))
                {
                    AddCable(cable);
                }
            }
        }

        /// <summary>
        /// Recalculate derived properties
        /// </summary>
        public void RecalculateProperties()
        {
            Weight = CalculateTotalCableWeight();
            Capacity = CalculateCapacityUtilization();
        }

        /// <summary>
        /// Create a copy of this tray segment model
        /// </summary>
        /// <returns>New tray segment model with copied properties</returns>
        public CableTraySegmentModel Clone()
        {
            var clone = new CableTraySegmentModel(CableTray)
            {
                TrayRef = TrayRef,
                Level = Level,
                Width = Width,
                Height = Height,
                Length = Length,
                Material = Material,
                Capacity = Capacity,
                Weight = Weight,
                BecaParametricCable = BecaParametricCable,
                SectionView = SectionView,
                IsSelected = IsSelected,
                IsModified = IsModified,
                Tag = Tag
            };

            // Copy cables
            if (Cables != null)
            {
                foreach (var cable in Cables)
                {
                    var clonedCable = cable.Clone();
                    clone.AddCable(clonedCable);
                }
            }

            // Copy additional properties
            if (AdditionalProperties != null)
            {
                clone.AdditionalProperties = new Dictionary<string, object>(AdditionalProperties);
            }

            return clone;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initialize properties from the tray instance
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        private void InitializeFromTray(CableTray cableTray)
        {
            try
            {
                // Get basic properties
                TrayRef = GetParameterValue(cableTray, "Mark") ?? cableTray.Id.ToString();
                
                // Get dimensions
                Width = GetParameterValueAsDouble(cableTray, "Width");
                Height = GetParameterValueAsDouble(cableTray, "Height");
                Length = GetParameterValueAsDouble(cableTray, "Length");

                // Get level
                Level = cableTray.Document.GetElement(cableTray.LevelId) as Level;

                // Get material
                Material = GetParameterValue(cableTray, "Material") ?? string.Empty;
            }
            catch (Exception)
            {
                // Handle any errors in parameter reading
                TrayRef = cableTray.Id.ToString();
            }
        }

        /// <summary>
        /// Get parameter value as string
        /// </summary>
        private string GetParameterValue(Element element, string parameterName)
        {
            try
            {
                var param = element.LookupParameter(parameterName);
                return param?.AsString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get parameter value as double
        /// </summary>
        private double GetParameterValueAsDouble(Element element, string parameterName)
        {
            try
            {
                var param = element.LookupParameter(parameterName);
                return param?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// String representation of the tray segment
        /// </summary>
        public override string ToString()
        {
            return $"Tray Segment: {TrayRef} - {NumberOfCables} cables ({Capacity:F1}% capacity)";
        }

        /// <summary>
        /// Equality comparison based on tray reference
        /// </summary>
        public override bool Equals(object obj)
        {
            if (obj is CableTraySegmentModel other)
            {
                return string.Equals(TrayRef, other.TrayRef, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Hash code based on tray reference
        /// </summary>
        public override int GetHashCode()
        {
            return TrayRef?.GetHashCode() ?? 0;
        }

        #endregion
    }
}
