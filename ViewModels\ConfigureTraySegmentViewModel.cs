using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.Input;
using MEP.TraylorSwift.Handlers;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.ComponentModel;
using System.Windows.Input;

namespace MEP.TraylorSwift.ViewModels
{
    /// <summary>
    /// ViewModel for Configure Tray Segment window
    /// </summary>
    public partial class ConfigureTraySegmentViewModel : BaseViewModel
    {
        #region Fields

        private readonly ISectionViewService _sectionViewService;
        private readonly IParametricCableService _parametricCableService;
        private readonly ITrayAnalysisService _trayAnalysisService;
        private CableTraySegmentModel _traySegment;
        private string _sectionViewName;
        private double _minimumSpaceCapacity = 80.0;
        private string _cableArrangement = "flat";
        private string _cableSpacing = "Touching";

        #endregion

        #region Properties

        /// <summary>
        /// The tray segment being configured
        /// </summary>
        public CableTraySegmentModel TraySegment
        {
            get => _traySegment;
            set
            {
                if (SetProperty(ref _traySegment, value))
                {
                    InitializeFromTraySegment();
                    OnPropertyChanged(nameof(HasSectionView));
                    OnPropertyChanged(nameof(CanCreateSection));
                }
            }
        }

        /// <summary>
        /// Section view name
        /// </summary>
        public string SectionViewName
        {
            get => _sectionViewName;
            set => SetProperty(ref _sectionViewName, value);
        }

        /// <summary>
        /// Minimum space capacity percentage
        /// </summary>
        public double MinimumSpaceCapacity
        {
            get => _minimumSpaceCapacity;
            set => SetProperty(ref _minimumSpaceCapacity, value);
        }

        /// <summary>
        /// Cable arrangement type
        /// </summary>
        public string CableArrangement
        {
            get => _cableArrangement;
            set => SetProperty(ref _cableArrangement, value);
        }

        /// <summary>
        /// Cable spacing type
        /// </summary>
        public string CableSpacing
        {
            get => _cableSpacing;
            set => SetProperty(ref _cableSpacing, value);
        }

        /// <summary>
        /// Whether the tray segment has a section view
        /// </summary>
        public bool HasSectionView => TraySegment?.SectionView != null;

        /// <summary>
        /// Whether a section can be created
        /// </summary>
        public bool CanCreateSection => !HasSectionView && !string.IsNullOrWhiteSpace(SectionViewName);

        #endregion

        #region Commands

        /// <summary>
        /// Command to create section view
        /// </summary>
        public RelayCommand CreateSectionCommand { get; }

        /// <summary>
        /// Command to update parametric cable parameters
        /// </summary>
        public RelayCommand UpdateParametersCommand { get; }

        /// <summary>
        /// Command to close the window
        /// </summary>
        public RelayCommand CloseCommand { get; }

        /// <summary>
        /// Command to navigate back to tray segments page
        /// </summary>
        public ICommand NavigateBackCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the window should be closed
        /// </summary>
        public event EventHandler CloseRequested;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the ConfigureTraySegmentViewModel
        /// </summary>
        public ConfigureTraySegmentViewModel(
            CableTraySegmentModel traySegment,
            ISectionViewService sectionViewService,
            IParametricCableService parametricCableService,
            ITrayAnalysisService trayAnalysisService,
            IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _sectionViewService = sectionViewService ?? throw new ArgumentNullException(nameof(sectionViewService));
            _parametricCableService = parametricCableService ?? throw new ArgumentNullException(nameof(parametricCableService));
            _trayAnalysisService = trayAnalysisService ?? throw new ArgumentNullException(nameof(trayAnalysisService));

            // Initialize commands
            CreateSectionCommand = new RelayCommand(CreateSection, () => CanCreateSection);
            UpdateParametersCommand = new RelayCommand(UpdateParameters, () => HasSectionView);
            CloseCommand = new RelayCommand(Close);
            NavigateBackCommand = new RelayCommand(NavigateBack);

            // Set tray segment
            TraySegment = traySegment;

            // Subscribe to property changes to update command states
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize properties from tray segment
        /// </summary>
        private void InitializeFromTraySegment()
        {
            if (TraySegment == null) return;

            // Set section view name
            if (TraySegment.SectionView != null)
            {
                SectionViewName = TraySegment.SectionView.Name;
            }
            else
            {
                SectionViewName = $"Section - {TraySegment.TrayRef}";
            }

            // Set active view to section view if it exists
            if (TraySegment.SectionView != null)
            {
                try
                {
                    // This would need to be done through external event
                    SharedData.SelectedTraySegmentForProcessing = TraySegment;
                    SharedData.ProcessingAction = "SetActiveView";
                }
                catch (Exception ex)
                {
                    _logger?.Log($"Failed to set active view: {ex.Message}", LogType.Warning);
                }
            }
        }

        /// <summary>
        /// Create section view
        /// </summary>
        private void CreateSection()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SectionViewName))
                {
                    StatusMessage = "Please enter a section view name.";
                    return;
                }

                // Validate section view name
                if (SectionViewName.IndexOfAny(System.IO.Path.GetInvalidFileNameChars()) >= 0)
                {
                    StatusMessage = "Section view name contains invalid characters.";
                    return;
                }

                IsLoading = true;
                StatusMessage = "Creating section view...";

                // Store data for external event processing
                SharedData.SelectedTraySegmentForProcessing = TraySegment;
                SharedData.SectionViewName = SectionViewName;
                SharedData.ProcessingAction = "CreateTraySection";

                // Request external event
                MakeRequest(RequestId.CreateTraySection);

                StatusMessage = "Section view creation requested.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error creating section: {ex.Message}";
                _logger?.Log($"Failed to create section: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Update parametric cable parameters
        /// </summary>
        private void UpdateParameters()
        {
            try
            {
                if (!HasSectionView)
                {
                    StatusMessage = "Section view must be created first.";
                    return;
                }

                IsLoading = true;
                StatusMessage = "Updating parametric cable parameters...";

                // Store configuration for external event processing
                SharedData.SelectedTraySegmentForProcessing = TraySegment;
                SharedData.ParametricCableConfig = new Handlers.ParametricCableConfiguration
                {
                    MinimumSpaceCapacity = MinimumSpaceCapacity,
                    CableArrangement = CableArrangement,
                    CableSpacing = CableSpacing
                };
                SharedData.ProcessingAction = "UpdateParametricCable";

                // Request external event
                MakeRequest(RequestId.UpdateParametricCable);

                StatusMessage = "Parameter update requested.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error updating parameters: {ex.Message}";
                _logger?.Log($"Failed to update parameters: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Close the window
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Navigate back to tray segments page
        /// </summary>
        private void NavigateBack()
        {
            // This will be handled by the parent ViewModel through event or navigation service
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Handle property changes to update command states
        /// </summary>
        private void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SectionViewName))
            {
                OnPropertyChanged(nameof(CanCreateSection));
                // Trigger CanExecuteChanged event manually
                CommandManager.InvalidateRequerySuggested();
            }
            else if (e.PropertyName == nameof(HasSectionView))
            {
                // Trigger CanExecuteChanged event manually
                CommandManager.InvalidateRequerySuggested();
            }
        }

        /// <summary>
        /// Override WakeUp to process SharedData after external events
        /// </summary>
        public override void WakeUp()
        {
            base.WakeUp();

            try
            {
                // Process SharedData based on the action performed
                if (!string.IsNullOrEmpty(SharedData.ProcessingAction))
                {
                    switch (SharedData.ProcessingAction)
                    {
                        case "CreateTraySection":
                            ProcessCreateTraySectionResponse();
                            break;
                        case "UpdateParametricCable":
                            ProcessUpdateParametricCableResponse();
                            break;
                    }

                    // Note: Don't clear ProcessingAction here as it might be used by other ViewModels
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing external event response: {ex.Message}";
                _logger?.Log($"Failed to process external event response: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Process the response from CreateTraySection external event
        /// </summary>
        private void ProcessCreateTraySectionResponse()
        {
            try
            {
                // The section view creation is handled in the RequestHandler
                // Update the UI to reflect the changes
                if (TraySegment != null)
                {
                    // Refresh the HasSectionView property
                    OnPropertyChanged(nameof(HasSectionView));
                    OnPropertyChanged(nameof(CanCreateSection));

                    // Update command states
                    CommandManager.InvalidateRequerySuggested();

                    StatusMessage = "Section view created successfully";
                    _logger?.Log($"Section view created for tray {TraySegment.TrayRef}", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing section creation response: {ex.Message}";
                _logger?.Log($"Failed to process section creation response: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Process the response from UpdateParametricCable external event
        /// </summary>
        private void ProcessUpdateParametricCableResponse()
        {
            try
            {
                // The parametric cable update is handled in the RequestHandler
                // Update the UI status
                StatusMessage = "Parametric cable parameters updated successfully";
                _logger?.Log($"Parametric cable updated for tray {TraySegment?.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing parametric cable update response: {ex.Message}";
                _logger?.Log($"Failed to process parametric cable update response: {ex.Message}", LogType.Error);
            }
        }
        #endregion
    }
}
