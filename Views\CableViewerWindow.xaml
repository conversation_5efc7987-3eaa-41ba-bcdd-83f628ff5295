<Window
    x:Class="MEP.TraylorSwift.Views.CableViewerWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Cable Viewer"
    Width="900"
    Height="600"
    MinWidth="700"
    MinHeight="400"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Margin="0,0,0,10"
                   Text="Cable Viewer" />

        <!-- Search and Filter -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="Search cables..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                <Button Grid.Column="2"
                       Command="{Binding ClearSearchCommand}"
                       Content="Clear" />
            </Grid>
        </materialDesign:Card>

        <!-- Cables DataGrid -->
        <materialDesign:Card Grid.Row="2" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Margin="0,0,0,10" Orientation="Horizontal">
                    <TextBlock Text="All Cables" />
                    <TextBlock Margin="10,0,0,0"
                              Text="{Binding CablesView.Count, StringFormat='({0} cables)'}" />
                </StackPanel>

                <DataGrid Grid.Row="1"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         ItemsSource="{Binding CablesView}"
                         SelectedItem="{Binding SelectedCable}"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Binding="{Binding CableRef}"
                                           Header="Cable Ref."
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding Name}"
                                           Header="Name"
                                           Width="150" />
                        <DataGridTextColumn Binding="{Binding GetToEquipmentName}"
                                           Header="To"
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding GetFromEquipmentName}"
                                           Header="From"
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding Diameter, StringFormat=F2}"
                                           Header="Diameter"
                                           Width="80" />
                        <DataGridTextColumn Binding="{Binding Weight, StringFormat=F2}"
                                           Header="Weight"
                                           Width="80" />
                        <DataGridTemplateColumn Header="Actions" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Command="{Binding DataContext.ShowInRevitCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                           CommandParameter="{Binding}"
                                           Content="Show in Revit"
                                           Height="30"
                                           FontSize="10" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Status and Actions -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                      VerticalAlignment="Center"
                      Text="{Binding StatusMessage}" />

            <Button Grid.Column="1"
                   Command="{Binding CloseCommand}"
                   Content="Close"/>
        </Grid>
    </Grid>
</Window>
