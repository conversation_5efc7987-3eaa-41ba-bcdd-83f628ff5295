# Traylor Swift - Quick Reference Cheat Sheet 🚀

## 🎯 **Quick Start (5 Steps)**
1. **Launch** → Add-ins tab → Traylor Swift
2. **Load Data** → Click "Load Data" button
3. **Add Trays** → "Add Tray Segment" → Select trays in Floor Plan
4. **Detect Cables** → Click "Detect Cables" button
5. **Create Tags** → Click "Create Tags" (Floor Plan view required)

---

## 🔧 **Main Window Controls**

| Button | Function | Requirements |
|--------|----------|--------------|
| **Load Data** | Load all cable trays & circuits | MEP project open |
| **Refresh Data** | Update with model changes | Data previously loaded |
| **Add Tray Segment** | Select tray in Revit | Floor Plan view |
| **Detect Cables** | Find cables in trays | Data loaded |
| **Create Tags** | Tag selected trays | Floor Plan view |
| **Create Section View** | Generate section views | Tray segments selected |
| **Create Parametric Cable** | Add 3D cable visualization | Section view exists |
| **Cable Viewer** | Browse all cables | Data loaded |

---

## 📋 **Tray Segments Grid Actions**

| Column | Action | Result |
|--------|--------|--------|
| **Configure** | Opens configure window | Detailed tray management |
| **Remove** | Removes tray from list | Tray no longer tracked |
| **Tray Ref** | Shows tray reference | Click to select in Revit |
| **Cables** | Shows cable count | From detection analysis |
| **Capacity %** | Shows tray utilization | Based on cable fill |

---

## ⚙️ **Configure Tray Segment Window**

### **Section View Creation**
```
1. Enter section view name (auto-generated)
2. Click "Create Section"
3. Section view opens automatically
4. TS_SectionView parameter updated
```

### **Parametric Cable Parameters**
| Parameter | Options | Description |
|-----------|---------|-------------|
| **Min Space Capacity** | 0-100% | Percentage of tray to keep free |
| **Cable Arrangement** | trefoil, flat | Physical cable layout |
| **Cable Spacing** | Touching, D, 2D | Distance between cables |

**Process:** Set parameters → Click "Update Parameters" → 3D cable created

---

## 🔍 **Cable Viewer Window**

### **Search & Filter**
- **Search Box** → Type to filter cables instantly
- **Clear Button** → Reset search filter
- **Cable Count** → Shows total and filtered results

### **Show in Revit**
```
1. Select cable in grid
2. Click "Show in Revit"
3. Creates TS_3DView_<Username>
4. Focuses on cable route
5. Optimized visibility settings
```

---

## 🏷️ **Tagging Quick Reference**

### **Requirements**
- ✅ Floor Plan view active
- ✅ Cable trays selected
- ✅ Tag family loaded

### **Tag Content**
- Tray reference number
- Cable count
- Capacity percentage
- Weight information

### **Auto Features**
- Optimal positioning
- Overlap avoidance
- Parameter updates

---

## 📐 **Section Views Quick Reference**

### **Creation Process**
```
Tray Segment → Configure → Section Name → Create Section
```

### **Features**
- Perpendicular cut through tray
- Shows cable arrangement
- Proper scale (1:50 default)
- Professional visibility settings

### **Uses**
- Design documentation
- Cable arrangement verification
- Construction drawings
- Capacity analysis

---

## 🔌 **Parametric Cables Quick Reference**

### **Prerequisites**
1. Section view must exist first
2. BecaParametricCable family loaded
3. Cables detected in tray

### **Configuration Options**

| Arrangement | Best For | Spacing Options |
|-------------|----------|-----------------|
| **trefoil** | Power cables | Touching, D, 2D |
| **flat** | Control cables | Touching, D, 2D |

### **Capacity Settings**
- **80%** → Standard practice
- **90%** → High utilization
- **70%** → Conservative approach

---

## 🚨 **Common Issues & Quick Fixes**

| Issue | Quick Fix |
|-------|-----------|
| **No trays found** | Check cable tray category & modeling |
| **Tags not created** | Switch to Floor Plan view |
| **Section view failed** | Ensure unique name & valid geometry |
| **Parametric cable error** | Create section view first |
| **Cable detection empty** | Check electrical circuits exist |
| **Floor Plan required** | Change active view to floor plan |

---

## 🎨 **View Requirements**

| Operation | Required View Type | Why |
|-----------|-------------------|-----|
| **Add Tray Segment** | Floor Plan | Element selection |
| **Create Tags** | Floor Plan | Tag placement |
| **Create Section** | Any | Section creation |
| **Parametric Cable** | Any | 3D element creation |
| **Cable Viewer** | Any | Independent window |

---

## 📊 **Data Flow**

```
1. Load Data → Cable Trays + Circuits loaded
2. Add Trays → Selected trays added to tracking
3. Detect Cables → Multi-criteria analysis
4. Create Tags → Visual documentation
5. Configure → Section views + Parametric cables
6. Cable Viewer → Project-wide analysis
```

---

## 🔄 **Workflow Patterns**

### **Standard Documentation Workflow**
```
Load → Add Trays → Detect → Tag → Configure → Section
```

### **Analysis Workflow**
```
Load → Detect → Cable Viewer → Show in Revit
```

### **Presentation Workflow**
```
Load → Add → Detect → Configure → Parametric Cables
```

---

## 💡 **Pro Tips**

### **Performance**
- Use Level Filter for large projects
- Refresh data after model changes
- Close unused windows

### **Quality**
- Review detected cables for accuracy
- Use consistent naming conventions
- Verify tag placement readability

### **Efficiency**
- Work level by level
- Batch similar operations
- Save project regularly

---

## 📝 **Parameter Reference**

| Parameter | Location | Purpose |
|-----------|----------|---------|
| **TS_Cables** | Cable Tray | Detected cable list |
| **TS_SectionView** | Cable Tray | Section view name |
| **TS_ParametricCable** | Cable Tray | Parametric cable ID |

---

## 🆘 **Emergency Troubleshooting**

### **Plugin Won't Start**
1. Check Revit version compatibility
2. Verify plugin installation
3. Restart Revit

### **No Data Loading**
1. Ensure MEP project is open
2. Check cable tray modeling
3. Verify electrical circuits exist

### **Operations Failing**
1. Check status messages
2. Verify view requirements
3. Ensure data is loaded

---

## 📞 **Support Checklist**

When reporting issues, include:
- [ ] Revit version
- [ ] Project type (MEP)
- [ ] Error message (exact text)
- [ ] Steps to reproduce
- [ ] Active view type
- [ ] Data loading status

---

## 🎉 **Success Indicators**

✅ **Data loaded successfully** → Tray segments appear in grid  
✅ **Cables detected** → Cable count > 0 in grid  
✅ **Tags created** → Tags visible in Floor Plan  
✅ **Section view created** → New view appears in Project Browser  
✅ **Parametric cable created** → 3D cable visible in section view  

**Happy cable tray management!** 🚀
