using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Structure;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of IParametricCableService for managing BecaParametricCable instances
    /// </summary>
    public class ParametricCableService : IParametricCableService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the parametric cable service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        public ParametricCableService(Document document, BecaActivityLoggerData logger)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
        }

        #endregion

        #region Parametric Cable Creation

        /// <summary>
        /// Create a parametric cable for a cable tray segment
        /// </summary>
        public FamilyInstance CreateParametricCable(CableTray cableTray, CableTraySegmentModel traySegment)
        {
            try
            {
                if (cableTray == null || traySegment == null) return null;

                // Get parametric cable family
                var cableFamily = GetParametricCableFamily();
                if (cableFamily == null)
                {
                    _logger?.Log("Parametric cable family not found", LogType.Error);
                    return null;
                }

                // Get tray centerline for cable placement
                var centerline = GetTrayCenterline(cableTray);
                if (centerline == null)
                {
                    _logger?.Log($"Could not get centerline for tray {traySegment.TrayRef}", LogType.Error);
                    return null;
                }

                using (var transaction = new Transaction(_document, "Create Parametric Cable"))
                {
                    transaction.Start();

                    // Create parametric cable instance
                    var cableInstance = CreateCableInstance(cableFamily, centerline, traySegment);
                    
                    if (cableInstance != null)
                    {
                        // Configure cable parameters
                        ConfigureCableParameters(cableInstance, traySegment);
                        
                        transaction.Commit();
                        _logger?.Log($"Created parametric cable for tray {traySegment.TrayRef}", LogType.Information);
                        return cableInstance;
                    }
                    else
                    {
                        transaction.RollBack();
                        _logger?.Log($"Failed to create parametric cable for tray {traySegment.TrayRef}", LogType.Error);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating parametric cable: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Create parametric cables for multiple tray segments
        /// </summary>
        public List<FamilyInstance> CreateMultipleParametricCables(List<CableTraySegmentModel> traySegments, ParametricCableOptions options)
        {
            var createdCables = new List<FamilyInstance>();

            try
            {
                if (traySegments == null || traySegments.Count == 0) return createdCables;

                using (var transaction = new Transaction(_document, "Create Multiple Parametric Cables"))
                {
                    transaction.Start();

                    foreach (var traySegment in traySegments)
                    {
                        try
                        {
                            if (options.SkipExistingCables && traySegment.BecaParametricCable != null)
                                continue;

                            var cableInstance = CreateParametricCable(traySegment.CableTray, traySegment);
                            if (cableInstance != null)
                            {
                                createdCables.Add(cableInstance);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.Log($"Failed to create parametric cable for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                        }
                    }

                    transaction.Commit();
                    _logger?.Log($"Created {createdCables.Count} parametric cables out of {traySegments.Count} tray segments", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating multiple parametric cables: {ex.Message}", LogType.Error);
            }

            return createdCables;
        }

        /// <summary>
        /// Create a parametric cable along a specific path
        /// </summary>
        public FamilyInstance CreateParametricCableAlongPath(Curve path, ParametricCableConfiguration config)
        {
            try
            {
                if (path == null || config == null) return null;

                var cableFamily = GetParametricCableFamily();
                if (cableFamily == null) return null;

                using (var transaction = new Transaction(_document, "Create Parametric Cable Along Path"))
                {
                    transaction.Start();

                    // Create cable instance along the path
                    var startPoint = path.GetEndPoint(0);
                    var endPoint = path.GetEndPoint(1);

                    // Get family symbol from the family
                    var symbol = _document.GetElement(cableFamily.GetFamilySymbolIds().First()) as FamilySymbol;
                    if (symbol == null) return null;

                    if (!symbol.IsActive)
                    {
                        symbol.Activate();
                    }

                    var cableInstance = _document.Create.NewFamilyInstance(startPoint, symbol, StructuralType.NonStructural);

                    if (cableInstance != null)
                    {
                        // Configure cable to follow the path
                        ConfigureCableAlongPath(cableInstance, path, config);
                        
                        transaction.Commit();
                        _logger?.Log("Created parametric cable along specified path", LogType.Information);
                        return cableInstance;
                    }
                    else
                    {
                        transaction.RollBack();
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating parametric cable along path: {ex.Message}", LogType.Error);
                return null;
            }
        }

        #endregion

        #region Parametric Cable Configuration

        /// <summary>
        /// Configure parametric cable parameters based on tray segment data
        /// </summary>
        public bool ConfigureCableParameters(FamilyInstance cableInstance, CableTraySegmentModel traySegment, ParametricCableConfiguration config)
        {
            try
            {
                if (cableInstance == null || traySegment == null) return false;

                using (var transaction = new Transaction(_document, "Configure Cable Parameters"))
                {
                    transaction.Start();

                    // Set cable count
                    SetParameterValue(cableInstance, "Cable_Count", traySegment.Cables.Count);

                    // Set cable diameter (average or largest)
                    var avgDiameter = CalculateAverageCableDiameter(traySegment.Cables);
                    SetParameterValue(cableInstance, "Cable_Diameter", avgDiameter);

                    // Set cable types
                    var cableTypes = GetUniqueCableTypes(traySegment.Cables);
                    SetParameterValue(cableInstance, "Cable_Types", string.Join(", ", cableTypes));

                    // Set tray reference
                    SetParameterValue(cableInstance, "Tray_Reference", traySegment.TrayRef);

                    // Set capacity utilization
                    SetParameterValue(cableInstance, "Capacity_Utilization", traySegment.Capacity);

                    // Apply configuration if provided
                    if (config != null)
                    {
                        ApplyParametricCableConfiguration(cableInstance, config);
                    }

                    transaction.Commit();
                    _logger?.Log($"Configured parametric cable parameters for tray {traySegment.TrayRef}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring cable parameters: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Update parametric cable with latest tray segment data
        /// </summary>
        public bool UpdateParametricCable(FamilyInstance cableInstance, CableTraySegmentModel traySegment)
        {
            try
            {
                if (cableInstance == null || traySegment == null) return false;

                using (var transaction = new Transaction(_document, "Update Parametric Cable"))
                {
                    transaction.Start();

                    // Update cable parameters with current data
                    ConfigureCableParameters(cableInstance, traySegment);

                    // Update cable geometry if needed
                    UpdateCableGeometry(cableInstance, traySegment);

                    transaction.Commit();
                    _logger?.Log($"Updated parametric cable for tray {traySegment.TrayRef}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error updating parametric cable: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Set cable visibility and display options
        /// </summary>
        public bool SetCableVisibility(FamilyInstance cableInstance, CableVisibilityOptions options)
        {
            try
            {
                if (cableInstance == null || options == null) return false;

                using (var transaction = new Transaction(_document, "Set Cable Visibility"))
                {
                    transaction.Start();

                    // Set visibility parameters
                    SetParameterValue(cableInstance, "Show_Individual_Cables", options.ShowIndividualCables);
                    SetParameterValue(cableInstance, "Show_Cable_Labels", options.ShowCableLabels);
                    SetParameterValue(cableInstance, "Show_Capacity_Fill", options.ShowCapacityFill);
                    SetParameterValue(cableInstance, "Cable_Transparency", options.CableTransparency);

                    // Set color coding
                    if (!string.IsNullOrEmpty(options.ColorScheme))
                    {
                        SetParameterValue(cableInstance, "Color_Scheme", options.ColorScheme);
                    }

                    transaction.Commit();
                    _logger?.Log("Set cable visibility options", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error setting cable visibility: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Parametric Cable Management

        /// <summary>
        /// Find existing parametric cables for a tray segment
        /// </summary>
        public List<FamilyInstance> FindExistingParametricCables(CableTraySegmentModel traySegment)
        {
            var existingCables = new List<FamilyInstance>();

            try
            {
                if (traySegment == null) return existingCables;

                // Check if tray segment already has a parametric cable reference
                if (traySegment.BecaParametricCable is FamilyInstance directCable)
                {
                    existingCables.Add(directCable);
                }

                // Search for parametric cables that might reference this tray
                var cableFamily = GetParametricCableFamily();
                if (cableFamily == null) return existingCables;

                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(FamilyInstance))
                    .OfType<FamilyInstance>()
                    .Where(fi => fi.Symbol.Family.Id == cableFamily.Id);

                foreach (var cableInstance in collector)
                {
                    try
                    {
                        var trayRefParam = cableInstance.LookupParameter("Tray_Reference");
                        if (trayRefParam != null && trayRefParam.AsString() == traySegment.TrayRef)
                        {
                            if (!existingCables.Contains(cableInstance))
                            {
                                existingCables.Add(cableInstance);
                            }
                        }
                    }
                    catch
                    {
                        // Skip cables that can't be processed
                    }
                }

                _logger?.Log($"Found {existingCables.Count} existing parametric cables for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding existing parametric cables: {ex.Message}", LogType.Error);
            }

            return existingCables;
        }

        /// <summary>
        /// Delete a parametric cable
        /// </summary>
        public bool DeleteParametricCable(FamilyInstance cableInstance)
        {
            try
            {
                if (cableInstance == null || !cableInstance.IsValidObject) return false;

                using (var transaction = new Transaction(_document, "Delete Parametric Cable"))
                {
                    transaction.Start();

                    _document.Delete(cableInstance.Id);

                    transaction.Commit();
                    _logger?.Log($"Deleted parametric cable {cableInstance.Id}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error deleting parametric cable: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Get all parametric cables in the document
        /// </summary>
        public List<FamilyInstance> GetAllParametricCables()
        {
            var parametricCables = new List<FamilyInstance>();

            try
            {
                var cableFamily = GetParametricCableFamily();
                if (cableFamily == null) return parametricCables;

                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(FamilyInstance))
                    .OfType<FamilyInstance>()
                    .Where(fi => fi.Symbol.Family.Id == cableFamily.Id);

                parametricCables.AddRange(collector);

                _logger?.Log($"Found {parametricCables.Count} parametric cables in document", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting all parametric cables: {ex.Message}", LogType.Error);
            }

            return parametricCables;
        }

        #endregion

        #region Cable Analysis

        /// <summary>
        /// Analyze parametric cable performance and optimization
        /// </summary>
        public ParametricCableAnalysis AnalyzeCablePerformance(FamilyInstance cableInstance, CableTraySegmentModel traySegment)
        {
            var analysis = new ParametricCableAnalysis();

            try
            {
                if (cableInstance == null || traySegment == null) return analysis;

                // Analyze cable count vs tray capacity
                analysis.CableCount = traySegment.Cables.Count;
                analysis.TrayCapacity = traySegment.Capacity;
                analysis.UtilizationEfficiency = CalculateUtilizationEfficiency(traySegment);

                // Analyze cable types and compatibility
                analysis.CableTypes = GetUniqueCableTypes(traySegment.Cables);
                analysis.TypeCompatibility = AnalyzeCableTypeCompatibility(traySegment.Cables);

                // Analyze geometric constraints
                analysis.GeometricConstraints = AnalyzeGeometricConstraints(cableInstance, traySegment);

                // Performance recommendations
                analysis.Recommendations = GeneratePerformanceRecommendations(analysis);

                _logger?.Log($"Analyzed parametric cable performance for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error analyzing cable performance: {ex.Message}", LogType.Error);
            }

            return analysis;
        }

        /// <summary>
        /// Generate cable routing optimization suggestions
        /// </summary>
        public List<string> GenerateRoutingOptimizations(List<CableTraySegmentModel> traySegments)
        {
            var optimizations = new List<string>();

            try
            {
                if (traySegments == null || traySegments.Count == 0) return optimizations;

                // Analyze capacity utilization across segments
                var underutilized = traySegments.Where(t => t.Capacity < 30).ToList();
                var overutilized = traySegments.Where(t => t.Capacity > 80).ToList();

                if (underutilized.Count > 0)
                {
                    optimizations.Add($"Consider consolidating cables in {underutilized.Count} underutilized tray segments");
                }

                if (overutilized.Count > 0)
                {
                    optimizations.Add($"Review {overutilized.Count} tray segments with high utilization (>80%)");
                }

                // Analyze cable type distribution
                var mixedTypeTrays = traySegments.Where(t => GetUniqueCableTypes(t.Cables).Count > 3).ToList();
                if (mixedTypeTrays.Count > 0)
                {
                    optimizations.Add($"Consider segregating cable types in {mixedTypeTrays.Count} tray segments");
                }

                _logger?.Log($"Generated {optimizations.Count} routing optimizations", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error generating routing optimizations: {ex.Message}", LogType.Error);
            }

            return optimizations;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate parametric cable creation requirements
        /// </summary>
        public List<string> ValidateParametricCableCreation(CableTraySegmentModel traySegment)
        {
            var issues = new List<string>();

            try
            {
                if (traySegment == null)
                {
                    issues.Add("Tray segment is null");
                    return issues;
                }

                if (traySegment.CableTray == null || !traySegment.CableTray.IsValidObject)
                {
                    issues.Add("Cable tray element is invalid");
                }

                if (traySegment.Cables == null || traySegment.Cables.Count == 0)
                {
                    issues.Add("No cables found in tray segment");
                }

                var cableFamily = GetParametricCableFamily();
                if (cableFamily == null)
                {
                    issues.Add("Parametric cable family not found");
                }

                var centerline = GetTrayCenterline(traySegment.CableTray);
                if (centerline == null)
                {
                    issues.Add("Could not determine tray centerline");
                }

                if (string.IsNullOrEmpty(traySegment.TrayRef))
                {
                    issues.Add("Tray reference is empty");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Check if parametric cable creation is supported for the tray type
        /// </summary>
        public bool IsParametricCableSupportedForTray(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return false;

                // Check if tray has valid geometry for cable creation
                var centerline = GetTrayCenterline(cableTray);
                return centerline != null;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Get the parametric cable family
        /// </summary>
        private Family GetParametricCableFamily()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(Family));

                foreach (Family family in collector)
                {
                    if (family.Name.Contains("BecaParametricCable") || 
                        family.Name.Contains("ParametricCable"))
                    {
                        return family;
                    }
                }

                // If not found, try to load it
                _logger?.Log("Parametric cable family not found - would need to load family", LogType.Warning);
                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting parametric cable family: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Get the centerline curve of a cable tray
        /// </summary>
        private Curve GetTrayCenterline(CableTray cableTray)
        {
            try
            {
                if (cableTray?.Location is LocationCurve locationCurve)
                {
                    return locationCurve.Curve;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting tray centerline: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Create cable instance along centerline
        /// </summary>
        private FamilyInstance CreateCableInstance(Family cableFamily, Curve centerline, CableTraySegmentModel traySegment)
        {
            try
            {
                var symbol = _document.GetElement(cableFamily.GetFamilySymbolIds().First()) as FamilySymbol;
                if (symbol == null) return null;

                if (!symbol.IsActive)
                {
                    symbol.Activate();
                }

                var startPoint = centerline.GetEndPoint(0);
                return _document.Create.NewFamilyInstance(startPoint, symbol, StructuralType.NonStructural);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating cable instance: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Configure cable parameters
        /// </summary>
        private void ConfigureCableParameters(FamilyInstance cableInstance, CableTraySegmentModel traySegment)
        {
            try
            {
                // Set basic parameters
                SetParameterValue(cableInstance, "Cable_Count", traySegment.Cables.Count);
                SetParameterValue(cableInstance, "Tray_Reference", traySegment.TrayRef);
                SetParameterValue(cableInstance, "Capacity_Utilization", traySegment.Capacity);

                // Set cable properties
                var avgDiameter = CalculateAverageCableDiameter(traySegment.Cables);
                SetParameterValue(cableInstance, "Cable_Diameter", avgDiameter);

                var cableTypes = GetUniqueCableTypes(traySegment.Cables);
                SetParameterValue(cableInstance, "Cable_Types", string.Join(", ", cableTypes));
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring cable parameters: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Configure cable along a specific path
        /// </summary>
        private void ConfigureCableAlongPath(FamilyInstance cableInstance, Curve path, ParametricCableConfiguration config)
        {
            try
            {
                // Set path-related parameters
                var pathLength = path.Length;
                SetParameterValue(cableInstance, "Cable_Length", pathLength);

                if (config != null)
                {
                    ApplyParametricCableConfiguration(cableInstance, config);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring cable along path: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Apply parametric cable configuration
        /// </summary>
        private void ApplyParametricCableConfiguration(FamilyInstance cableInstance, ParametricCableConfiguration config)
        {
            try
            {
                if (config.CableCount.HasValue)
                    SetParameterValue(cableInstance, "Cable_Count", config.CableCount.Value);

                if (config.CableDiameter.HasValue)
                    SetParameterValue(cableInstance, "Cable_Diameter", config.CableDiameter.Value);

                if (!string.IsNullOrEmpty(config.CableType))
                    SetParameterValue(cableInstance, "Cable_Type", config.CableType);

                if (!string.IsNullOrEmpty(config.ColorScheme))
                    SetParameterValue(cableInstance, "Color_Scheme", config.ColorScheme);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error applying cable configuration: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Update cable geometry
        /// </summary>
        private void UpdateCableGeometry(FamilyInstance cableInstance, CableTraySegmentModel traySegment)
        {
            try
            {
                // Update cable geometry based on current tray configuration
                // This would involve updating the cable path to match the tray centerline
                _logger?.Log($"Updated cable geometry for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error updating cable geometry: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Set parameter value safely
        /// </summary>
        private void SetParameterValue(FamilyInstance element, string parameterName, object value)
        {
            try
            {
                var parameter = element.LookupParameter(parameterName);
                if (parameter != null && !parameter.IsReadOnly)
                {
                    switch (value)
                    {
                        case int intValue:
                            parameter.Set(intValue);
                            break;
                        case double doubleValue:
                            parameter.Set(doubleValue);
                            break;
                        case string stringValue:
                            parameter.Set(stringValue);
                            break;
                        case bool boolValue:
                            parameter.Set(boolValue ? 1 : 0);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error setting parameter {parameterName}: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Calculate average cable diameter
        /// </summary>
        private double CalculateAverageCableDiameter(ObservableCollection<CableModel> cables)
        {
            try
            {
                if (cables == null || cables.Count == 0) return 0.0;

                var totalDiameter = cables.Sum(c => c.Diameter);
                return totalDiameter / cables.Count;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get unique cable types
        /// </summary>
        private List<string> GetUniqueCableTypes(ObservableCollection<CableModel> cables)
        {
            try
            {
                if (cables == null || cables.Count == 0) return new List<string>();

                return cables.Select(c => c.CableType).Distinct().ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// Calculate utilization efficiency
        /// </summary>
        private double CalculateUtilizationEfficiency(CableTraySegmentModel traySegment)
        {
            try
            {
                // Efficiency based on capacity utilization
                var capacity = traySegment.Capacity;
                
                if (capacity < 30) return 0.5; // Underutilized
                if (capacity > 80) return 0.7; // Overutilized
                
                return 1.0; // Optimal range
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Analyze cable type compatibility
        /// </summary>
        private double AnalyzeCableTypeCompatibility(ObservableCollection<CableModel> cables)
        {
            try
            {
                var uniqueTypes = GetUniqueCableTypes(cables);
                
                // Fewer types = better compatibility
                if (uniqueTypes.Count <= 2) return 1.0;
                if (uniqueTypes.Count <= 4) return 0.8;
                
                return 0.6;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Analyze geometric constraints
        /// </summary>
        private List<string> AnalyzeGeometricConstraints(FamilyInstance cableInstance, CableTraySegmentModel traySegment)
        {
            var constraints = new List<string>();

            try
            {
                // Check for tight bends
                var centerline = GetTrayCenterline(traySegment.CableTray);
                if (centerline != null && centerline is Arc arc)
                {
                    var radius = arc.Radius;
                    if (radius < 2.0) // Less than 2 feet
                    {
                        constraints.Add("Tight bend radius may affect cable performance");
                    }
                }

                // Check for capacity constraints
                if (traySegment.Capacity > 80)
                {
                    constraints.Add("High capacity utilization may require larger tray");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error analyzing geometric constraints: {ex.Message}", LogType.Warning);
            }

            return constraints;
        }

        /// <summary>
        /// Generate performance recommendations
        /// </summary>
        private List<string> GeneratePerformanceRecommendations(ParametricCableAnalysis analysis)
        {
            var recommendations = new List<string>();

            try
            {
                if (analysis.UtilizationEfficiency < 0.7)
                {
                    recommendations.Add("Consider optimizing cable routing for better efficiency");
                }

                if (analysis.TypeCompatibility < 0.8)
                {
                    recommendations.Add("Consider segregating incompatible cable types");
                }

                if (analysis.TrayCapacity > 80)
                {
                    recommendations.Add("Consider using larger tray or additional parallel trays");
                }

                if (analysis.GeometricConstraints.Count > 0)
                {
                    recommendations.Add("Review geometric constraints for cable installation");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error generating recommendations: {ex.Message}", LogType.Warning);
            }

            return recommendations;
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Analysis results for parametric cable performance
    /// </summary>
    public class ParametricCableAnalysis
    {
        public int CableCount { get; set; }
        public double TrayCapacity { get; set; }
        public double UtilizationEfficiency { get; set; }
        public List<string> CableTypes { get; set; } = new List<string>();
        public double TypeCompatibility { get; set; }
        public List<string> GeometricConstraints { get; set; } = new List<string>();
        public List<string> Recommendations { get; set; } = new List<string>();
    }

    #endregion
}
