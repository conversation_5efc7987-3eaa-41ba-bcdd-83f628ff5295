using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI.Selection;

namespace MEP.TraylorSwift.Handlers
{
    /// <summary>
    /// Selection filter for cable tray elements
    /// </summary>
    public class CableTraySelectionFilter : ISelectionFilter
    {
        /// <summary>
        /// Allow selection of cable tray elements only
        /// </summary>
        public bool AllowElement(Element elem)
        {
            if (elem is CableTray familyInstance)
            {
                return familyInstance.Category?.Id?.IntegerValue == (int)BuiltInCategory.OST_CableTray;
            }
            return false;
        }

        /// <summary>
        /// Allow selection of cable tray references
        /// </summary>
        public bool AllowReference(Reference reference, XYZ position)
        {
            return true;
        }
    }
}
