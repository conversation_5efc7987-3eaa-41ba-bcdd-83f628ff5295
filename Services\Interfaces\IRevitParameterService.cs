using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for reading and writing Revit parameters, specifically TS_* parameters
    /// </summary>
    public interface IRevitParameterService
    {
        #region TS_Cables Parameter

        /// <summary>
        /// Read TS_Cables parameter from an element
        /// </summary>
        /// <param name="element">Element to read parameter from</param>
        /// <returns>Comma-separated cable references or empty string if not found</returns>
        string ReadTSCablesParameter(Element element);

        /// <summary>
        /// Write TS_Cables parameter to an element
        /// </summary>
        /// <param name="element">Element to write parameter to</param>
        /// <param name="cableReferences">Comma-separated cable references</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSCablesParameter(Element element, string cableReferences);

        /// <summary>
        /// Parse TS_Cables parameter value into individual cable references
        /// </summary>
        /// <param name="parameterValue">Parameter value to parse</param>
        /// <returns>List of individual cable references</returns>
        List<string> ParseTSCablesParameter(string parameterValue);

        /// <summary>
        /// Format cable references into TS_Cables parameter value
        /// </summary>
        /// <param name="cableReferences">List of cable references</param>
        /// <returns>Formatted parameter value</returns>
        string FormatTSCablesParameter(List<string> cableReferences);

        #endregion

        #region TS_ParametricCable Parameter

        /// <summary>
        /// Read TS_ParametricCable parameter from an element
        /// </summary>
        /// <param name="element">Element to read parameter from</param>
        /// <returns>Element ID of parametric cable or null if not found</returns>
        ElementId ReadTSParametricCableParameter(Element element);

        /// <summary>
        /// Write TS_ParametricCable parameter to an element
        /// </summary>
        /// <param name="element">Element to write parameter to</param>
        /// <param name="parametricCableId">Element ID of parametric cable</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSParametricCableParameter(Element element, ElementId parametricCableId);

        /// <summary>
        /// Clear TS_ParametricCable parameter from an element
        /// </summary>
        /// <param name="element">Element to clear parameter from</param>
        /// <returns>True if parameter was cleared successfully</returns>
        bool ClearTSParametricCableParameter(Element element);

        #endregion

        #region TS_SectionView Parameter

        /// <summary>
        /// Read TS_SectionView parameter from an element
        /// </summary>
        /// <param name="element">Element to read parameter from</param>
        /// <returns>Element ID of section view or null if not found</returns>
        ElementId ReadTSSectionViewParameter(Element element);

        /// <summary>
        /// Write TS_SectionView parameter to an element
        /// </summary>
        /// <param name="element">Element to write parameter to</param>
        /// <param name="sectionViewId">Element ID of section view</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSSectionViewParameter(Element element, ElementId sectionViewId);

        /// <summary>
        /// Clear TS_SectionView parameter from an element
        /// </summary>
        /// <param name="element">Element to clear parameter from</param>
        /// <returns>True if parameter was cleared successfully</returns>
        bool ClearTSSectionViewParameter(Element element);

        #endregion

        #region Parameter Management

        /// <summary>
        /// Check if an element has a specific parameter
        /// </summary>
        /// <param name="element">Element to check</param>
        /// <param name="parameterName">Name of parameter to check for</param>
        /// <returns>True if element has the parameter</returns>
        bool HasParameter(Element element, string parameterName);

        /// <summary>
        /// Get parameter by name from an element
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Name of parameter to get</param>
        /// <returns>Parameter or null if not found</returns>
        Parameter GetParameter(Element element, string parameterName);

        /// <summary>
        /// Get parameter value as string
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <returns>Parameter value as string or null if not found</returns>
        string GetParameterValueAsString(Element element, string parameterName);

        /// <summary>
        /// Get parameter value as double
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <returns>Parameter value as double or 0.0 if not found</returns>
        double GetParameterValueAsDouble(Element element, string parameterName);

        /// <summary>
        /// Get parameter value as integer
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <returns>Parameter value as integer or 0 if not found</returns>
        int GetParameterValueAsInteger(Element element, string parameterName);

        /// <summary>
        /// Get parameter value as ElementId
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <returns>Parameter value as ElementId or null if not found</returns>
        ElementId GetParameterValueAsElementId(Element element, string parameterName);

        /// <summary>
        /// Set parameter value as string
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <param name="value">Value to set</param>
        /// <returns>True if parameter was set successfully</returns>
        bool SetParameterValue(Element element, string parameterName, string value);

        /// <summary>
        /// Set parameter value as double
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <param name="value">Value to set</param>
        /// <returns>True if parameter was set successfully</returns>
        bool SetParameterValue(Element element, string parameterName, double value);

        /// <summary>
        /// Set parameter value as integer
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <param name="value">Value to set</param>
        /// <returns>True if parameter was set successfully</returns>
        bool SetParameterValue(Element element, string parameterName, int value);

        /// <summary>
        /// Set parameter value as ElementId
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="parameterName">Name of parameter</param>
        /// <param name="value">Value to set</param>
        /// <returns>True if parameter was set successfully</returns>
        bool SetParameterValue(Element element, string parameterName, ElementId value);

        #endregion

        #region Parameter Creation

        /// <summary>
        /// Create TS_* parameters if they don't exist
        /// </summary>
        /// <returns>True if parameters were created or already exist</returns>
        //bool EnsureTSParametersExist();

        /// <summary>
        /// Create a shared parameter
        /// </summary>
        /// <param name="parameterName">Name of parameter to create</param>
        /// <param name="parameterType">Type of parameter</param>
        /// <param name="categorySet">Categories to apply parameter to</param>
        /// <param name="parameterGroup">Parameter group</param>
        /// <param name="isInstance">True for instance parameter, false for type parameter</param>
        /// <returns>True if parameter was created successfully</returns>
        //bool CreateSharedParameter(string parameterName, ParameterType parameterType, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool isInstance = true);

        /// <summary>
        /// Create a project parameter
        /// </summary>
        /// <param name="parameterName">Name of parameter to create</param>
        /// <param name="parameterType">Type of parameter</param>
        /// <param name="categorySet">Categories to apply parameter to</param>
        /// <param name="parameterGroup">Parameter group</param>
        /// <param name="isInstance">True for instance parameter, false for type parameter</param>
        /// <returns>True if parameter was created successfully</returns>
        //bool CreateProjectParameter(string parameterName, ParameterType parameterType, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool isInstance = true);

        #endregion

        #region Batch Operations

        /// <summary>
        /// Read TS_Cables parameters from multiple elements
        /// </summary>
        /// <param name="elements">Elements to read parameters from</param>
        /// <returns>Dictionary mapping element IDs to cable references</returns>
        Dictionary<ElementId, string> ReadTSCablesParametersFromElements(List<Element> elements);

        /// <summary>
        /// Write TS_Cables parameters to multiple elements
        /// </summary>
        /// <param name="elementCableMap">Dictionary mapping element IDs to cable references</param>
        /// <returns>Number of elements successfully updated</returns>
        int WriteTSCablesParametersToElements(Dictionary<ElementId, string> elementCableMap);

        /// <summary>
        /// Clear all TS_* parameters from an element
        /// </summary>
        /// <param name="element">Element to clear parameters from</param>
        /// <returns>Number of parameters cleared</returns>
        int ClearAllTSParameters(Element element);

        /// <summary>
        /// Clear all TS_* parameters from multiple elements
        /// </summary>
        /// <param name="elements">Elements to clear parameters from</param>
        /// <returns>Total number of parameters cleared</returns>
        int ClearAllTSParametersFromElements(List<Element> elements);

        #endregion

        #region Validation

        /// <summary>
        /// Validate TS_Cables parameter value format
        /// </summary>
        /// <param name="parameterValue">Parameter value to validate</param>
        /// <returns>True if format is valid</returns>
        bool ValidateTSCablesParameterFormat(string parameterValue);

        /// <summary>
        /// Validate that referenced elements exist
        /// </summary>
        /// <param name="element">Element with TS_* parameters</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateTSParameterReferences(Element element);

        /// <summary>
        /// Find elements with invalid TS_* parameter values
        /// </summary>
        /// <param name="elements">Elements to check</param>
        /// <returns>List of elements with invalid parameters</returns>
        List<Element> FindElementsWithInvalidTSParameters(List<Element> elements);

        #endregion

        #region Utilities

        /// <summary>
        /// Get all elements with TS_Cables parameter set
        /// </summary>
        /// <returns>List of elements with TS_Cables parameter</returns>
        List<Element> GetElementsWithTSCablesParameter();

        /// <summary>
        /// Get all elements with TS_ParametricCable parameter set
        /// </summary>
        /// <returns>List of elements with TS_ParametricCable parameter</returns>
        List<Element> GetElementsWithTSParametricCableParameter();

        /// <summary>
        /// Get all elements with TS_SectionView parameter set
        /// </summary>
        /// <returns>List of elements with TS_SectionView parameter</returns>
        List<Element> GetElementsWithTSSectionViewParameter();

        /// <summary>
        /// Get statistics about TS_* parameter usage
        /// </summary>
        /// <returns>Statistics about parameter usage</returns>
        TSParameterStatistics GetTSParameterStatistics();

        /// <summary>
        /// Export TS_* parameter data to dictionary
        /// </summary>
        /// <param name="elements">Elements to export data from</param>
        /// <returns>Dictionary containing parameter data</returns>
        Dictionary<string, object> ExportTSParameterData(List<Element> elements);

        /// <summary>
        /// Import TS_* parameter data from dictionary
        /// </summary>
        /// <param name="parameterData">Parameter data to import</param>
        /// <returns>Number of elements updated</returns>
        int ImportTSParameterData(Dictionary<string, object> parameterData);

        #endregion
    }

    /// <summary>
    /// Statistics about TS_* parameter usage
    /// </summary>
    public class TSParameterStatistics
    {
        public int ElementsWithTSCables { get; set; }
        public int ElementsWithTSParametricCable { get; set; }
        public int ElementsWithTSSectionView { get; set; }
        public int TotalCableReferences { get; set; }
        public int UniqueCableReferences { get; set; }
        public int OrphanedParametricCables { get; set; }
        public int OrphanedSectionViews { get; set; }
        public List<string> MostCommonCableReferences { get; set; }
        public DateTime LastUpdated { get; set; }

        public TSParameterStatistics()
        {
            MostCommonCableReferences = new List<string>();
            LastUpdated = DateTime.Now;
        }

        public override string ToString()
        {
            return $"TS Parameters - Cables: {ElementsWithTSCables}, Parametric: {ElementsWithTSParametricCable}, Sections: {ElementsWithTSSectionView}";
        }
    }

    /// <summary>
    /// Constants for TS_* parameter names
    /// </summary>
    public static class TSParameterNames
    {
        public const string TS_Cables = "TS_Cables";
        public const string TS_ParametricCable = "TS_ParametricCable";
        public const string TS_SectionView = "TS_SectionView";
    }

    /// <summary>
    /// Parameter operation result
    /// </summary>
    public class ParameterOperationResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public string ParameterName { get; set; }
        public ElementId ElementId { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }

        public ParameterOperationResult(bool success, string parameterName, ElementId elementId)
        {
            Success = success;
            ParameterName = parameterName;
            ElementId = elementId;
        }

        public ParameterOperationResult(bool success, string parameterName, ElementId elementId, string errorMessage) 
            : this(success, parameterName, elementId)
        {
            ErrorMessage = errorMessage;
        }
    }
}
