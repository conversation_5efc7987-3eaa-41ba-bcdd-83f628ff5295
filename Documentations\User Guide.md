# Traylor Swift MEP Plugin - User Guide 📖

## Overview
Traylor Swift is a powerful Revit plugin designed for MEP engineers to efficiently manage cable tray systems, detect cables, create tags, generate section views, and visualize cable arrangements with parametric cable families.

## Table of Contents
1. [Getting Started](#getting-started)
2. [Main Interface](#main-interface)
3. [Core Features](#core-features)
4. [Configure Tray Segment](#configure-tray-segment)
5. [Cable Viewer](#cable-viewer)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

---

## Getting Started

### Prerequisites
- **Autodesk Revit** (2019 or later)
- **MEP Project** with cable trays and electrical circuits
- **BecaParametricCable Family** (for parametric cable visualization)

### Launching the Plugin
1. Open your MEP project in Revit
2. Navigate to the **Add-ins** tab
3. Click **Traylor Swift** button
4. The modeless main window will appear

### Initial Setup
- Ensure your project has cable trays modeled
- Verify electrical circuits are defined
- Check that cable tray parameters (TS_Cables, TS_SectionView, TS_ParametricCable) exist

---

## Main Interface

### Window Layout
The main window consists of:
- **Data Loading Section** - Load and refresh project data
- **Tray Management** - Add/remove tray segments
- **Cable Detection** - Detect cables in trays
- **Tagging Tools** - Create and manage tags
- **Visualization Tools** - Section views and parametric cables
- **Cable Management** - Access cable viewer
- **Tray Segments Grid** - View and manage all tray segments

### Key Controls
- **Load Data** - Loads all cable trays and circuits from the project
- **Refresh Data** - Updates data with latest project changes
- **Level Filter** - Filter tray segments by building level
- **Status Bar** - Shows current operation status and progress

---

## Core Features

### 1. Data Loading 📊

**Purpose:** Load cable trays and electrical circuits from your Revit project.

**Steps:**
1. Click **"Load Data"** button
2. Wait for data loading to complete
3. Review loaded tray segments in the grid
4. Use **Level Filter** to focus on specific floors

**What Gets Loaded:**
- All cable tray family instances
- Electrical circuits and equipment
- Existing cable data from TS_Cables parameters
- Section views and parametric cables

### 2. Adding Tray Segments ➕

**Purpose:** Add new cable trays to the analysis system.

**Steps:**
1. Click **"Add Tray Segment"** button
2. **Switch to Floor Plan view** (required for tagging)
3. Select a cable tray in the Revit model
4. The tray will be added to the segments grid

**Important Notes:**
- Only works in Floor Plan views
- Cable trays must be properly modeled family instances
- Duplicate trays are automatically filtered out

### 3. Cable Detection 🔍

**Purpose:** Automatically detect which cables run through each cable tray.

**Steps:**
1. Ensure data is loaded
2. Click **"Detect Cables"** button
3. Wait for multi-criteria analysis to complete
4. Review detected cables in the grid

**Detection Methods:**
- **Geometric Intersection** - Cables physically intersecting trays
- **Circuit Path Analysis** - Following electrical circuit routing
- **Proximity Analysis** - Cables near tray paths
- **Confidence Scoring** - Weighted results for accuracy

### 4. Tagging 🏷️

**Purpose:** Create tags for cable trays showing cable information.

**Steps:**
1. Ensure you're in a **Floor Plan view**
2. Select tray segments to tag
3. Click **"Create Tags"** button
4. Tags will be placed automatically with optimal positioning

**Tag Features:**
- Shows tray reference and cable count
- Automatic positioning to avoid overlaps
- Updates when cable data changes
- Writes detected cables to TS_Cables parameter

### 5. Section Views 📐

**Purpose:** Create cross-sectional views of cable trays showing cable arrangements.

**Steps:**
1. Select a tray segment
2. Click **"Configure"** button in the grid
3. Enter section view name
4. Click **"Create Section"** in the configure window
5. Section view will be created and activated

**Section View Features:**
- Perpendicular cut through tray
- Shows cable arrangement and spacing
- Proper scale and visibility settings
- Linked to tray via TS_SectionView parameter

### 6. Parametric Cables 🔌

**Purpose:** Create 3D parametric cable representations in trays.

**Steps:**
1. Create section view first (required)
2. In Configure window, set parameters:
   - **Minimum Space Capacity** (%)
   - **Cable Arrangement** (trefoil/flat)
   - **Cable Spacing** (Touching/D/2D)
3. Click **"Update Parameters"**
4. Parametric cable will be created/updated

**Parametric Cable Benefits:**
- 3D visualization of cable arrangements
- Capacity analysis and optimization
- Professional presentation drawings
- Integration with BecaParametricCable family

---

## Configure Tray Segment

### Opening Configure Window
1. In the main grid, click **"Configure"** button for any tray segment
2. Configure window opens showing tray details

### Cable Information
- **Cables Grid** - Shows all detected cables in the tray
- **Cable Properties** - Reference, name, endpoints, diameter, weight
- **Read-only Display** - Information for reference only

### Section View Creation
1. **Section Name** - Auto-generated or enter custom name
2. **Create Section** - Click to generate cross-sectional view
3. **View Activation** - Section view opens automatically
4. **Parameter Update** - TS_SectionView parameter updated

### Parametric Cable Configuration
**Available after section view creation:**

1. **Minimum Space Capacity** - Percentage of tray capacity to maintain
2. **Cable Arrangement Options:**
   - **trefoil** - Triangular arrangement for power cables
   - **flat** - Single layer arrangement
3. **Cable Spacing Options:**
   - **Touching** - Cables touching each other
   - **D** - One diameter spacing between cables
   - **2D** - Two diameter spacing between cables

### Update Process
1. Set desired parameters
2. Click **"Update Parameters"**
3. Parametric cable family instance created/updated
4. TS_ParametricCable parameter updated with element reference

---

## Cable Viewer

### Opening Cable Viewer
1. Click **"Cable Viewer"** button in main window
2. Cable Viewer window opens showing all project cables

### Cable Browser
- **Search Function** - Filter cables by reference, name, or equipment
- **Cable Grid** - Shows all cables with properties
- **Real-time Filtering** - Results update as you type
- **Cable Count** - Total and filtered counts displayed

### Show in Revit Feature
1. Select any cable in the grid
2. Click **"Show in Revit"** button
3. **3D View Creation:**
   - Creates `TS_3DView_<YourUsername>`
   - Sets section box around cable route
   - Configures visibility for cable systems
   - Activates view and zooms to cable

### 3D View Features
- **Focused View** - Section box around cable path
- **Optimized Visibility** - Shows only relevant categories
- **Professional Presentation** - Shaded visual style
- **User-Specific** - Each user gets their own 3D view

---

## Best Practices

### Project Setup
1. **Model cable trays accurately** with proper family instances
2. **Define electrical circuits** with proper routing
3. **Use consistent naming** for trays and equipment
4. **Add custom parameters** (TS_Cables, TS_SectionView, TS_ParametricCable)

### Workflow Recommendations
1. **Load data first** before any operations
2. **Work in Floor Plan views** for tagging operations
3. **Detect cables before tagging** for accurate information
4. **Create section views** before parametric cables
5. **Use Cable Viewer** for project-wide cable analysis

### Performance Tips
1. **Filter by level** to work with smaller datasets
2. **Refresh data** after making model changes
3. **Close unused windows** to maintain performance
4. **Save project regularly** during intensive operations

### Quality Control
1. **Review detected cables** for accuracy
2. **Verify tag placement** and readability
3. **Check section views** for proper scale and content
4. **Validate parametric cables** against design requirements

---

## Troubleshooting

### Common Issues

**"No cable trays found"**
- Ensure cable trays are modeled as family instances
- Check that trays are in OST_CableTray category
- Verify project has cable tray elements

**"Tags not created"**
- Switch to Floor Plan view
- Ensure tag family is loaded in project
- Check that tray segments are selected

**"Section view creation failed"**
- Verify tray has valid geometry
- Check section view name is unique
- Ensure sufficient space around tray

**"Parametric cable not created"**
- Create section view first
- Load BecaParametricCable family
- Check tray has detected cables

### Error Messages
- **"Floor Plan Required"** - Switch to floor plan view for tagging
- **"No data loaded"** - Click Load Data button first
- **"Invalid selection"** - Select valid cable tray elements
- **"Parameter not found"** - Add required TS_* parameters to families

### Getting Help
1. Check status messages in the main window
2. Review Revit warnings and errors
3. Ensure all prerequisites are met
4. Contact support with specific error messages

---

## Summary

Traylor Swift streamlines MEP cable tray documentation by:
- **Automating cable detection** with intelligent analysis
- **Creating professional tags** with accurate information
- **Generating section views** for detailed documentation
- **Visualizing cable arrangements** with parametric families
- **Providing comprehensive cable management** tools

The plugin integrates seamlessly with Revit workflows while maintaining data integrity through proper parameter management and transaction handling.

**Happy cable tray management!** 🚀
