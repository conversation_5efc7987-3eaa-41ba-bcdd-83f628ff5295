﻿<Page x:Class="MEP.TraylorSwift.Views.Pages.TraySegmentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.TraylorSwift.Views.Pages"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:converters="clr-namespace:MEP.TraylorSwift.Converters"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="TraySegmentsPage">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />

        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200" />
        </Grid.RowDefinitions>

        <!--  Tray Segments List  -->
        <materialDesign:Card Grid.Row="0" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Text="Cable Tray Segments" />

                <DataGrid
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    ItemsSource="{Binding TraySegmentsView}"
                    SelectedItem="{Binding SelectedTraySegment}"
                    SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn
                            Width="100"
                            Binding="{Binding TrayRef}"
                            Header="Tray Ref" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Width, StringFormat=F2}"
                            Header="Width" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Cables.Count}"
                            Header="Cables" />
                        <DataGridTextColumn
                            Width="100"
                            Binding="{Binding Capacity, StringFormat=F1}"
                            Header="Capacity %" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Weight, StringFormat=F1}"
                            Header="Weight" />
                        <DataGridTemplateColumn Width="100" Header="Configure">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30"
                                        Command="{Binding DataContext.ConfigureTraySegmentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="Configure"
                                        FontSize="10" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="100" Header="Remove">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30"
                                        Command="{Binding DataContext.RemoveTraySegmentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="Remove"
                                        FontSize="10" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!--  Splitter  -->
        <GridSplitter
            Grid.Row="1"
            Height="5"
            HorizontalAlignment="Stretch" />

        <!--  Selected Tray Details  -->
        <materialDesign:Card Grid.Row="2" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Text="Selected Tray Details" />

                <ScrollViewer
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    VerticalScrollBarVisibility="Auto">
                    <StackPanel DataContext="{Binding SelectedTraySegment}">
                        <TextBlock Text="{Binding TrayRef, StringFormat='Tray Reference: {0}'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Width, StringFormat='Width: {0:F2} ft'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Height, StringFormat='Height: {0:F2} ft'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Capacity, StringFormat='Capacity: {0:F1}%'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Cables.Count, StringFormat='Number of Cables: {0}'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />

                        <TextBlock
                            Margin="0,10,0,5"
                            Text="Cables:"
                            Visibility="{Binding Cables.Count, Converter={StaticResource CountToVisibilityConverter}}" />

                        <ItemsControl ItemsSource="{Binding Cables}" Visibility="{Binding Cables.Count, Converter={StaticResource CountToVisibilityConverter}}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Margin="10,0,0,2" Text="{Binding CableRef}" />
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
