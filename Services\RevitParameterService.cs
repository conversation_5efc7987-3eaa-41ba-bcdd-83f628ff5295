using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of IRevitParameterService for managing TS_* parameters
    /// </summary>
    public class RevitParameterService : IRevitParameterService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the Revit parameter service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        public RevitParameterService(Document document, BecaActivityLoggerData logger)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
        }

        #endregion

        #region TS_Cables Parameter

        /// <summary>
        /// Read TS_Cables parameter from an element
        /// </summary>
        public string ReadTSCablesParameter(Element element)
        {
            try
            {
                return GetParameterValueAsString(element, TSParameterNames.TS_Cables) ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to read TS_Cables parameter from element {element?.Id}: {ex.Message}", LogType.Error);
                return string.Empty;
            }
        }

        /// <summary>
        /// Write TS_Cables parameter to an element
        /// </summary>
        public bool WriteTSCablesParameter(Element element, string cableReferences)
        {
            try
            {
                return SetParameterValue(element, TSParameterNames.TS_Cables, cableReferences ?? string.Empty);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_Cables parameter to element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Parse TS_Cables parameter value into individual cable references
        /// </summary>
        public List<string> ParseTSCablesParameter(string parameterValue)
        {
            if (string.IsNullOrEmpty(parameterValue))
                return new List<string>();

            return parameterValue
                .Split(',')
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();
        }

        /// <summary>
        /// Format cable references into TS_Cables parameter value
        /// </summary>
        public string FormatTSCablesParameter(List<string> cableReferences)
        {
            if (cableReferences == null || cableReferences.Count == 0)
                return string.Empty;

            var validReferences = cableReferences
                .Where(r => !string.IsNullOrEmpty(r))
                .Select(r => r.Trim())
                .Distinct()
                .ToList();

            return string.Join(",", validReferences);
        }

        #endregion

        #region TS_ParametricCable Parameter

        /// <summary>
        /// Read TS_ParametricCable parameter from an element
        /// </summary>
        public ElementId ReadTSParametricCableParameter(Element element)
        {
            try
            {
                return GetParameterValueAsElementId(element, TSParameterNames.TS_ParametricCable);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to read TS_ParametricCable parameter from element {element?.Id}: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Write TS_ParametricCable parameter to an element
        /// </summary>
        public bool WriteTSParametricCableParameter(Element element, ElementId parametricCableId)
        {
            try
            {
                return SetParameterValue(element, TSParameterNames.TS_ParametricCable, parametricCableId);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_ParametricCable parameter to element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Clear TS_ParametricCable parameter from an element
        /// </summary>
        public bool ClearTSParametricCableParameter(Element element)
        {
            try
            {
                return SetParameterValue(element, TSParameterNames.TS_ParametricCable, ElementId.InvalidElementId);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to clear TS_ParametricCable parameter from element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region TS_SectionView Parameter

        /// <summary>
        /// Read TS_SectionView parameter from an element
        /// </summary>
        public ElementId ReadTSSectionViewParameter(Element element)
        {
            try
            {
                return GetParameterValueAsElementId(element, TSParameterNames.TS_SectionView);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to read TS_SectionView parameter from element {element?.Id}: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Write TS_SectionView parameter to an element
        /// </summary>
        public bool WriteTSSectionViewParameter(Element element, ElementId sectionViewId)
        {
            try
            {
                return SetParameterValue(element, TSParameterNames.TS_SectionView, sectionViewId);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_SectionView parameter to element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Clear TS_SectionView parameter from an element
        /// </summary>
        public bool ClearTSSectionViewParameter(Element element)
        {
            try
            {
                return SetParameterValue(element, TSParameterNames.TS_SectionView, ElementId.InvalidElementId);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to clear TS_SectionView parameter from element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Parameter Management

        /// <summary>
        /// Check if an element has a specific parameter
        /// </summary>
        public bool HasParameter(Element element, string parameterName)
        {
            try
            {
                if (element == null || string.IsNullOrEmpty(parameterName))
                    return false;

                var parameter = element.LookupParameter(parameterName);
                return parameter != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get parameter by name from an element
        /// </summary>
        public Parameter GetParameter(Element element, string parameterName)
        {
            try
            {
                if (element == null || string.IsNullOrEmpty(parameterName))
                    return null;

                return element.LookupParameter(parameterName);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get parameter value as string
        /// </summary>
        public string GetParameterValueAsString(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || !parameter.HasValue)
                    return null;

                return parameter.AsString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get parameter value as double
        /// </summary>
        public double GetParameterValueAsDouble(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || !parameter.HasValue)
                    return 0.0;

                return parameter.AsDouble();
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get parameter value as integer
        /// </summary>
        public int GetParameterValueAsInteger(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || !parameter.HasValue)
                    return 0;

                return parameter.AsInteger();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Get parameter value as ElementId
        /// </summary>
        public ElementId GetParameterValueAsElementId(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || !parameter.HasValue)
                    return null;

                var elementId = parameter.AsElementId();
                return elementId != ElementId.InvalidElementId ? elementId : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Set parameter value as string
        /// </summary>
        public bool SetParameterValue(Element element, string parameterName, string value)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || parameter.IsReadOnly)
                    return false;

                return parameter.Set(value ?? string.Empty);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set string parameter {parameterName} on element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Set parameter value as double
        /// </summary>
        public bool SetParameterValue(Element element, string parameterName, double value)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || parameter.IsReadOnly)
                    return false;

                return parameter.Set(value);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set double parameter {parameterName} on element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Set parameter value as integer
        /// </summary>
        public bool SetParameterValue(Element element, string parameterName, int value)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || parameter.IsReadOnly)
                    return false;

                return parameter.Set(value);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set integer parameter {parameterName} on element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Set parameter value as ElementId
        /// </summary>
        public bool SetParameterValue(Element element, string parameterName, ElementId value)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter == null || parameter.IsReadOnly)
                    return false;

                return parameter.Set(value ?? ElementId.InvalidElementId);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set ElementId parameter {parameterName} on element {element?.Id}: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Parameter Creation

        /// <summary>
        /// Create TS_* parameters if they don't exist
        /// </summary>
        //public bool EnsureTSParametersExist()
        //{
        //    try
        //    {
        //        var categorySet = _document.Application.Create.NewCategorySet();
        //        var cableTrayCategory = Category.GetCategory(_document, BuiltInCategory.OST_CableTray);
        //        if (cableTrayCategory != null)
        //        {
        //            categorySet.Insert(cableTrayCategory);
        //        }

        //        bool success = true;

        //        // Create TS_Cables parameter (Text)
        //        if (!ParameterExists(TSParameterNames.TS_Cables))
        //        {
        //            success &= CreateProjectParameter(
        //                TSParameterNames.TS_Cables,
        //                ParameterType.Text,
        //                categorySet,
        //                BuiltInParameterGroup.PG_DATA,
        //                true);
        //        }

        //        // Create TS_ParametricCable parameter (ElementId)
        //        if (!ParameterExists(TSParameterNames.TS_ParametricCable))
        //        {
        //            success &= CreateProjectParameter(
        //                TSParameterNames.TS_ParametricCable,
        //                ParameterType.FamilyType,
        //                categorySet,
        //                BuiltInParameterGroup.PG_DATA,
        //                true);
        //        }

        //        // Create TS_SectionView parameter (ElementId)
        //        if (!ParameterExists(TSParameterNames.TS_SectionView))
        //        {
        //            success &= CreateProjectParameter(
        //                TSParameterNames.TS_SectionView,
        //                ParameterType.FamilyType,
        //                categorySet,
        //                BuiltInParameterGroup.PG_DATA,
        //                true);
        //        }

        //        return success;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger?.Log($"Failed to ensure TS parameters exist: {ex.Message}", LogType.Error);
        //        return false;
        //    }
        //}

        /// <summary>
        /// Create a shared parameter
        /// </summary>
        //public bool CreateSharedParameter(string parameterName, ParameterType parameterType, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool isInstance = true)
        //{
        //    try
        //    {
        //        // This is a simplified implementation
        //        // In a real implementation, we would need to handle shared parameter files
        //        _logger?.Log($"Shared parameter creation not implemented for {parameterName}", LogType.Warning);
        //        return false;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger?.Log($"Failed to create shared parameter {parameterName}: {ex.Message}", LogType.Error);
        //        return false;
        //    }
        //}

        ///// <summary>
        ///// Create a project parameter
        ///// </summary>
        //public bool CreateProjectParameter(string parameterName, ParameterType parameterType, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool isInstance = true)
        //{
        //    try
        //    {
        //        // This is a simplified implementation
        //        // In a real implementation, we would use the Revit API to create project parameters
        //        _logger?.Log($"Project parameter creation not fully implemented for {parameterName}", LogType.Warning);
        //        return true; // Return true for now to allow testing
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger?.Log($"Failed to create project parameter {parameterName}: {ex.Message}", LogType.Error);
        //        return false;
        //    }
        //}

        #endregion

        #region Batch Operations

        /// <summary>
        /// Read TS_Cables parameters from multiple elements
        /// </summary>
        public Dictionary<ElementId, string> ReadTSCablesParametersFromElements(List<Element> elements)
        {
            var result = new Dictionary<ElementId, string>();

            if (elements == null) return result;

            foreach (var element in elements)
            {
                try
                {
                    var cableRefs = ReadTSCablesParameter(element);
                    if (!string.IsNullOrEmpty(cableRefs))
                    {
                        result[element.Id] = cableRefs;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.Log($"Failed to read TS_Cables from element {element.Id}: {ex.Message}", LogType.Error);
                }
            }

            return result;
        }

        /// <summary>
        /// Write TS_Cables parameters to multiple elements
        /// </summary>
        public int WriteTSCablesParametersToElements(Dictionary<ElementId, string> elementCableMap)
        {
            int successCount = 0;

            if (elementCableMap == null) return successCount;

            foreach (var kvp in elementCableMap)
            {
                try
                {
                    var element = _document.GetElement(kvp.Key);
                    if (element != null && WriteTSCablesParameter(element, kvp.Value))
                    {
                        successCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.Log($"Failed to write TS_Cables to element {kvp.Key}: {ex.Message}", LogType.Error);
                }
            }

            return successCount;
        }

        /// <summary>
        /// Clear all TS_* parameters from an element
        /// </summary>
        public int ClearAllTSParameters(Element element)
        {
            int clearedCount = 0;

            if (element == null) return clearedCount;

            try
            {
                if (WriteTSCablesParameter(element, string.Empty))
                    clearedCount++;

                if (ClearTSParametricCableParameter(element))
                    clearedCount++;

                if (ClearTSSectionViewParameter(element))
                    clearedCount++;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to clear TS parameters from element {element.Id}: {ex.Message}", LogType.Error);
            }

            return clearedCount;
        }

        /// <summary>
        /// Clear all TS_* parameters from multiple elements
        /// </summary>
        public int ClearAllTSParametersFromElements(List<Element> elements)
        {
            int totalCleared = 0;

            if (elements == null) return totalCleared;

            foreach (var element in elements)
            {
                totalCleared += ClearAllTSParameters(element);
            }

            return totalCleared;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate TS_Cables parameter value format
        /// </summary>
        public bool ValidateTSCablesParameterFormat(string parameterValue)
        {
            if (string.IsNullOrEmpty(parameterValue))
                return true; // Empty is valid

            try
            {
                var cableRefs = ParseTSCablesParameter(parameterValue);
                return cableRefs.All(r => !string.IsNullOrWhiteSpace(r));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validate that referenced elements exist
        /// </summary>
        public List<string> ValidateTSParameterReferences(Element element)
        {
            var issues = new List<string>();

            if (element == null) return issues;

            try
            {
                // Check TS_ParametricCable reference
                var parametricCableId = ReadTSParametricCableParameter(element);
                if (parametricCableId != null)
                {
                    var parametricCable = _document.GetElement(parametricCableId);
                    if (parametricCable == null || !parametricCable.IsValidObject)
                    {
                        issues.Add($"TS_ParametricCable references invalid element: {parametricCableId}");
                    }
                }

                // Check TS_SectionView reference
                var sectionViewId = ReadTSSectionViewParameter(element);
                if (sectionViewId != null)
                {
                    var sectionView = _document.GetElement(sectionViewId);
                    if (sectionView == null || !sectionView.IsValidObject)
                    {
                        issues.Add($"TS_SectionView references invalid element: {sectionViewId}");
                    }
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Error validating TS parameter references: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Find elements with invalid TS_* parameter values
        /// </summary>
        public List<Element> FindElementsWithInvalidTSParameters(List<Element> elements)
        {
            var invalidElements = new List<Element>();

            if (elements == null) return invalidElements;

            foreach (var element in elements)
            {
                try
                {
                    var issues = ValidateTSParameterReferences(element);
                    if (issues.Count > 0)
                    {
                        invalidElements.Add(element);
                    }

                    var cableRefs = ReadTSCablesParameter(element);
                    if (!ValidateTSCablesParameterFormat(cableRefs))
                    {
                        invalidElements.Add(element);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.Log($"Error validating element {element.Id}: {ex.Message}", LogType.Error);
                    invalidElements.Add(element);
                }
            }

            return invalidElements.Distinct().ToList();
        }

        #endregion

        #region Utilities

        /// <summary>
        /// Get all elements with TS_Cables parameter set
        /// </summary>
        public List<Element> GetElementsWithTSCablesParameter()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType();

                return collector
                    .Where(e => !string.IsNullOrEmpty(ReadTSCablesParameter(e)))
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get elements with TS_Cables parameter: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        /// <summary>
        /// Get all elements with TS_ParametricCable parameter set
        /// </summary>
        public List<Element> GetElementsWithTSParametricCableParameter()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType();

                return collector
                    .Where(e => ReadTSParametricCableParameter(e) != null)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get elements with TS_ParametricCable parameter: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        /// <summary>
        /// Get all elements with TS_SectionView parameter set
        /// </summary>
        public List<Element> GetElementsWithTSSectionViewParameter()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType();

                return collector
                    .Where(e => ReadTSSectionViewParameter(e) != null)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get elements with TS_SectionView parameter: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        /// <summary>
        /// Get statistics about TS_* parameter usage
        /// </summary>
        public TSParameterStatistics GetTSParameterStatistics()
        {
            var stats = new TSParameterStatistics();

            try
            {
                var elementsWithCables = GetElementsWithTSCablesParameter();
                stats.ElementsWithTSCables = elementsWithCables.Count;

                stats.ElementsWithTSParametricCable = GetElementsWithTSParametricCableParameter().Count;
                stats.ElementsWithTSSectionView = GetElementsWithTSSectionViewParameter().Count;

                // Calculate cable reference statistics
                var allCableRefs = new List<string>();
                foreach (var element in elementsWithCables)
                {
                    var cableRefs = ParseTSCablesParameter(ReadTSCablesParameter(element));
                    allCableRefs.AddRange(cableRefs);
                }

                stats.TotalCableReferences = allCableRefs.Count;
                stats.UniqueCableReferences = allCableRefs.Distinct().Count();

                // Find most common cable references
                stats.MostCommonCableReferences = allCableRefs
                    .GroupBy(r => r)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => g.Key)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get TS parameter statistics: {ex.Message}", LogType.Error);
            }

            return stats;
        }

        /// <summary>
        /// Export TS_* parameter data to dictionary
        /// </summary>
        public Dictionary<string, object> ExportTSParameterData(List<Element> elements)
        {
            var data = new Dictionary<string, object>();

            try
            {
                var elementData = new List<Dictionary<string, object>>();

                foreach (var element in elements ?? new List<Element>())
                {
                    var elementInfo = new Dictionary<string, object>
                    {
                        ["ElementId"] = element.Id.IntegerValue,
                        ["TS_Cables"] = ReadTSCablesParameter(element),
                        ["TS_ParametricCable"] = ReadTSParametricCableParameter(element)?.IntegerValue,
                        ["TS_SectionView"] = ReadTSSectionViewParameter(element)?.IntegerValue
                    };

                    elementData.Add(elementInfo);
                }

                data["Elements"] = elementData;
                data["ExportDate"] = DateTime.Now;
                data["DocumentTitle"] = _document.Title;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export TS parameter data: {ex.Message}", LogType.Error);
            }

            return data;
        }

        /// <summary>
        /// Import TS_* parameter data from dictionary
        /// </summary>
        public int ImportTSParameterData(Dictionary<string, object> parameterData)
        {
            int updatedCount = 0;

            try
            {
                if (parameterData?.ContainsKey("Elements") == true)
                {
                    var elementDataList = parameterData["Elements"] as List<Dictionary<string, object>>;
                    if (elementDataList != null)
                    {
                        foreach (var elementData in elementDataList)
                        {
                            try
                            {
                                var elementIdValue = elementData["ElementId"];
                                if (elementIdValue != null)
                                {
                                    var elementId = new ElementId(Convert.ToInt32(elementIdValue));
                                    var element = _document.GetElement(elementId);

                                    if (element != null)
                                    {
                                        bool updated = false;

                                        if (elementData.ContainsKey("TS_Cables"))
                                        {
                                            var cableRefs = elementData["TS_Cables"]?.ToString();
                                            if (WriteTSCablesParameter(element, cableRefs))
                                                updated = true;
                                        }

                                        if (updated)
                                            updatedCount++;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger?.Log($"Failed to import data for element: {ex.Message}", LogType.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to import TS parameter data: {ex.Message}", LogType.Error);
            }

            return updatedCount;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Check if a parameter exists in the document
        /// </summary>
        private bool ParameterExists(string parameterName)
        {
            try
            {
                // This is a simplified check - in a real implementation we would check the parameter bindings
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType()
                    .FirstOrDefault();

                return collector != null && HasParameter(collector, parameterName);
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
